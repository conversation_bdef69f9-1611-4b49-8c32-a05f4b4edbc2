{"name": "cf-admin-panel-structure-v1", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^7.0.0", "@fortawesome/free-regular-svg-icons": "^7.0.0", "@fortawesome/free-solid-svg-icons": "^7.0.0", "@fortawesome/react-fontawesome": "^0.2.2", "@react-router/node": "^7.7.0", "@react-router/serve": "^7.7.0", "ag-grid-community": "^34.0.2", "ag-grid-react": "^34.0.2", "antd": "^5.26.6", "classnames": "^2.5.1", "dayjs": "^1.11.13", "isbot": "^5.1.27", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.7.0", "throttle-debounce": "^5.0.2"}, "devDependencies": {"@react-router/dev": "^7.7.0", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}