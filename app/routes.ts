import {
  type RouteConfig,
  index,
  route,
  layout,
} from "@react-router/dev/routes";

export default [
  layout("pages/Layout.tsx", [
    index("pages/Dashboard.tsx"),
    route("login", "pages/auth/Login.tsx"),
    route("dashboard", "pages/Dashboard.tsx"),
    route("users", "pages/users/UserList.tsx"),
    route("users/:id", "pages/users/UserDetail.tsx"),
    route("users/create", "pages/users/UserCreate.tsx"),
    route("users/:id/edit", "pages/users/UserEdit.tsx"),
    route("settings", "pages/settings/Settings.tsx"),
    route("profile", "pages/Profile.tsx"),
    route("reports", "pages/reports/Reports.tsx"),
  ]),
] satisfies RouteConfig;
