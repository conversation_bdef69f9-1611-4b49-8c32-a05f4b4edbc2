// Global TypeScript type definitions and interfaces

// API Response Types
interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: string;
}

interface PaginatedResponse<T = unknown> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Authentication Types
interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  permissions: Permission[];
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

// User Roles and Permissions
enum UserRole {
  SUPER_ADMIN = "super_admin",
  ADMIN = "admin",
  MANAGER = "manager",
  USER = "user",
  VIEWER = "viewer",
}

enum Permission {
  READ_USERS = "read_users",
  WRITE_USERS = "write_users",
  DELETE_USERS = "delete_users",
  READ_SETTINGS = "read_settings",
  WRITE_SETTINGS = "write_settings",
  READ_REPORTS = "read_reports",
  WRITE_REPORTS = "write_reports",
  ADMIN_ACCESS = "admin_access",
}

// HTTP Types
interface HttpRequestConfig {
  method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  headers?: Record<string, string>;
  body?: unknown;
  credentials?: RequestCredentials;
  signal?: AbortSignal;
}

interface HttpResponse<T = unknown> {
  data: T;
  status: number;
  statusText: string;
  headers: Headers;
}

interface HttpError {
  message: string;
  status?: number;
  statusText?: string;
  data?: unknown;
}

// Form Types
interface FormField {
  name: string;
  label: string;
  type: FormFieldType;
  required?: boolean;
  placeholder?: string;
  defaultValue?: unknown;
  validation?: ValidationRule[];
  options?: SelectOption[];
  disabled?: boolean;
  hidden?: boolean;
  dependencies?: FieldDependency[];
  gridProps?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
}

enum FormFieldType {
  TEXT = "text",
  EMAIL = "email",
  PASSWORD = "password",
  NUMBER = "number",
  TEXTAREA = "textarea",
  SELECT = "select",
  MULTI_SELECT = "multi_select",
  CHECKBOX = "checkbox",
  RADIO = "radio",
  DATE = "date",
  DATE_RANGE = "date_range",
  TIME = "time",
  FILE = "file",
  SWITCH = "switch",
  SLIDER = "slider",
  RATE = "rate",
  COLOR = "color",
}

interface SelectOption {
  label: string;
  value: string | number;
  disabled?: boolean;
  children?: SelectOption[];
}

interface ValidationRule {
  type: "required" | "min" | "max" | "pattern" | "custom";
  value?: string | number;
  message: string;
  validator?: (value: unknown) => boolean | Promise<boolean>;
}

interface FieldDependency {
  field: string;
  condition: "equals" | "not_equals" | "contains" | "not_contains";
  value: unknown;
  action: "show" | "hide" | "enable" | "disable";
}

interface FormConfig {
  title?: string;
  description?: string;
  fields: FormField[];
  layout?: "horizontal" | "vertical" | "inline";
  submitText?: string;
  resetText?: string;
  showReset?: boolean;
  onSubmit?: (values: Record<string, unknown>) => void | Promise<void>;
  onReset?: () => void;
  initialValues?: Record<string, unknown>;
}

// Table/Grid Types
interface TableColumn<T = unknown> {
  key: string;
  title: string;
  dataIndex?: string;
  width?: number;
  fixed?: "left" | "right";
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: unknown, record: T, index: number) => React.ReactNode;
  align?: "left" | "center" | "right";
}

interface TableConfig<T = unknown> {
  columns: TableColumn<T>[];
  dataSource: T[];
  loading?: boolean;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
  };
  rowSelection?: {
    type: "checkbox" | "radio";
    selectedRowKeys: React.Key[];
    onChange: (selectedRowKeys: React.Key[], selectedRows: T[]) => void;
  };
  onRow?: (record: T, index?: number) => React.HTMLAttributes<HTMLElement>;
}

// Route Types
interface RouteConfig {
  path: string;
  obfuscatedPath?: string;
  component: React.ComponentType;
  exact?: boolean;
  title?: string;
  description?: string;
  requiresAuth?: boolean;
  requiredPermissions?: Permission[];
  layout?: "default" | "auth" | "minimal";
  meta?: Record<string, unknown>;
}

// Responsive Types
interface Breakpoint {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  xxl: number;
}

interface ViewportSize {
  width: number;
  height: number;
  breakpoint: keyof Breakpoint;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
}

// Theme Types
interface ThemeConfig {
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  borderRadius: number;
  fontSize: {
    small: number;
    medium: number;
    large: number;
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
}

// Utility Types
type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Event Types
interface AppEvent<T = unknown> {
  type: string;
  payload?: T;
  timestamp: number;
  source?: string;
}

// Error Types
interface AppError {
  code: string;
  message: string;
  details?: unknown;
  timestamp: number;
  stack?: string;
}

// Loading States
interface LoadingState {
  isLoading: boolean;
  error: AppError | null;
  lastUpdated?: number;
}

// Generic CRUD Operations
interface CrudOperations<T> {
  create: (data: Omit<T, "id">) => Promise<T>;
  read: (id: string) => Promise<T>;
  update: (id: string, data: Partial<T>) => Promise<T>;
  delete: (id: string) => Promise<void>;
  list: (params?: Record<string, unknown>) => Promise<PaginatedResponse<T>>;
}
