import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router';
import {
  Layout,
  Menu,
  Button,
  Avatar,
  Dropdown,
  Space,
  Typography,
  Switch,
  Breadcrumb
} from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useAuth, useResize } from '@/hooks';
import { navigation } from '@/config/routes';
import { ICONS } from '@/config/icons';
import { themeManager } from '@/config/theme';

/**
 * Main application layout component
 * Responsive sidebar navigation with Ant Design
 * Integrated with authentication and theming
 */

const { Header, Sider, Content, Footer } = Layout;
const { Title, Text } = Typography;

export default function AppLayout() {
  const [collapsed, setCollapsed] = useState(false);
  const [darkMode, setDarkMode] = useState(themeManager.getCurrentTheme() === 'dark');
  const { user, logout } = useAuth();
  const { isMobile } = useResize();
  const navigate = useNavigate();
  const location = useLocation();

  // Handle theme toggle
  const handleThemeToggle = (checked: boolean) => {
    setDarkMode(checked);
    themeManager.setTheme(checked ? 'dark' : 'light');
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
      navigate(navigation.login());
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  // User dropdown menu
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Profile',
      onClick: () => navigate(navigation.profile())
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Settings',
      onClick: () => navigate(navigation.settings())
    },
    {
      type: 'divider' as const
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
      onClick: handleLogout
    }
  ];

  // Navigation menu items
  const menuItems = [
    {
      key: navigation.dashboard(),
      icon: <FontAwesomeIcon icon={ICONS.DASHBOARD} />,
      label: 'Dashboard'
    },
    {
      key: navigation.users(),
      icon: <FontAwesomeIcon icon={ICONS.USERS} />,
      label: 'Users'
    },
    {
      key: navigation.reports(),
      icon: <FontAwesomeIcon icon={ICONS.HOME} />,
      label: 'Reports'
    },
    {
      key: navigation.settings(),
      icon: <FontAwesomeIcon icon={ICONS.SETTINGS} />,
      label: 'Settings'
    }
  ];

  // Handle menu click
  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  // Generate breadcrumb items
  const getBreadcrumbItems = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const items = [
      {
        title: (
          <span>
            <FontAwesomeIcon icon={ICONS.HOME} className="mr-1" />
            Home
          </span>
        )
      }
    ];

    pathSegments.forEach((segment, index) => {
      const path = '/' + pathSegments.slice(0, index + 1).join('/');
      items.push({
        title: segment.charAt(0).toUpperCase() + segment.slice(1)
      });
    });

    return items;
  };

  return (
    <Layout className="min-h-screen">
      {/* Sidebar */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        breakpoint="lg"
        collapsedWidth={isMobile ? 0 : 80}
        onBreakpoint={(broken) => {
          if (broken && !collapsed) {
            setCollapsed(true);
          }
        }}
        className="shadow-lg"
        style={{
          position: 'fixed',
          height: '100vh',
          left: 0,
          top: 0,
          bottom: 0,
          zIndex: 100
        }}
      >
        {/* Logo */}
        <div className="h-16 flex items-center justify-center border-b border-gray-700">
          {collapsed ? (
            <Title level={4} className="text-white m-0">
              CF
            </Title>
          ) : (
            <Title level={4} className="text-white m-0">
              CF Admin
            </Title>
          )}
        </div>

        {/* Navigation Menu */}
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          className="border-r-0"
        />
      </Sider>

      {/* Main Layout */}
      <Layout
        style={{
          marginLeft: collapsed ? (isMobile ? 0 : 80) : 200,
          transition: 'margin-left 0.2s'
        }}
      >
        {/* Header */}
        <Header
          className="bg-white shadow-sm border-b border-gray-200 px-4 flex items-center justify-between"
          style={{
            position: 'sticky',
            top: 0,
            zIndex: 99,
            width: '100%'
          }}
        >
          <div className="flex items-center">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="text-lg"
            />
            
            <Breadcrumb
              items={getBreadcrumbItems()}
              className="ml-4"
            />
          </div>

          <div className="flex items-center space-x-4">
            {/* Theme Toggle */}
            <Space>
              <Text>Dark Mode</Text>
              <Switch
                checked={darkMode}
                onChange={handleThemeToggle}
                size="small"
              />
            </Space>

            {/* User Menu */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <Space className="cursor-pointer hover:bg-gray-50 px-2 py-1 rounded">
                <Avatar
                  size="small"
                  icon={<UserOutlined />}
                  src={user?.avatar}
                />
                <Text>{user?.firstName} {user?.lastName}</Text>
              </Space>
            </Dropdown>
          </div>
        </Header>

        {/* Content */}
        <Content
          className="p-6 bg-gray-50"
          style={{
            minHeight: 'calc(100vh - 64px - 70px)' // Subtract header and footer height
          }}
        >
          <div className="bg-white rounded-lg shadow-sm p-6">
            <Outlet />
          </div>
        </Content>

        {/* Footer */}
        <Footer className="text-center bg-white border-t border-gray-200">
          <Text type="secondary">
            CF Admin Panel ©2024 Created with React Router v7, TypeScript, and Ant Design
          </Text>
        </Footer>
      </Layout>
    </Layout>
  );
}
