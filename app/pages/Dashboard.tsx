import React, { useState, useEffect } from "react";
import {
  Row,
  Col,
  Card,
  Statistic,
  Progress,
  Typography,
  Space,
  Button,
  DatePicker,
  Select,
} from "antd";
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  UserOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { DataTable } from "@/components/common/DataTable";
import { CustomForm } from "@/components/forms/CustomForm";
import { useHttp, useResize } from "@/hooks";
import { ICONS } from "@/config/icons";

/**
 * Dashboard page demonstrating AG Grid tables, Ant Design components, and CustomForm
 * Responsive design with comprehensive data visualization
 */

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

interface DashboardStats {
  totalUsers: number;
  totalOrders: number;
  totalRevenue: number;
  totalViews: number;
  userGrowth: number;
  orderGrowth: number;
  revenueGrowth: number;
  viewGrowth: number;
}

interface RecentActivity {
  id: string;
  user: string;
  action: string;
  timestamp: string;
  status: "success" | "warning" | "error";
}

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [activities, setActivities] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const { isMobile, isTablet } = useResize();
  const { execute: fetchData } = useHttp();

  // Load dashboard data
  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Simulate API calls - replace with real endpoints
      const mockStats: DashboardStats = {
        totalUsers: 1234,
        totalOrders: 567,
        totalRevenue: 89012,
        totalViews: 34567,
        userGrowth: 12.5,
        orderGrowth: -2.3,
        revenueGrowth: 8.7,
        viewGrowth: 15.2,
      };

      const mockActivities: RecentActivity[] = [
        {
          id: "1",
          user: "John Doe",
          action: "Created new order #1234",
          timestamp: "2024-01-15 10:30:00",
          status: "success",
        },
        {
          id: "2",
          user: "Jane Smith",
          action: "Updated user profile",
          timestamp: "2024-01-15 10:25:00",
          status: "success",
        },
        {
          id: "3",
          user: "Bob Johnson",
          action: "Failed login attempt",
          timestamp: "2024-01-15 10:20:00",
          status: "error",
        },
        {
          id: "4",
          user: "Alice Brown",
          action: "Exported report",
          timestamp: "2024-01-15 10:15:00",
          status: "warning",
        },
      ];

      setStats(mockStats);
      setActivities(mockActivities);
    } catch (error) {
      console.error("Failed to load dashboard data:", error);
    } finally {
      setLoading(false);
    }
  };

  // Table configuration for recent activities
  const activitiesTableConfig: TableConfig<RecentActivity> = {
    columns: [
      {
        key: "user",
        title: "User",
        dataIndex: "user",
        sortable: true,
        filterable: true,
      },
      {
        key: "action",
        title: "Action",
        dataIndex: "action",
        sortable: true,
      },
      {
        key: "timestamp",
        title: "Timestamp",
        dataIndex: "timestamp",
        sortable: true,
      },
      {
        key: "status",
        title: "Status",
        dataIndex: "status",
        render: (status: string) => {
          const colors = {
            success: "text-green-600",
            warning: "text-yellow-600",
            error: "text-red-600",
          };
          return (
            <span
              className={
                colors[status as keyof typeof colors] || "text-gray-600"
              }
            >
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </span>
          );
        },
      },
    ],
    dataSource: activities,
    loading,
    pagination: {
      current: 1,
      pageSize: 5,
      total: activities.length,
      showSizeChanger: false,
      showQuickJumper: false,
    },
  };

  // Form configuration for dashboard filters
  const filterFormConfig: FormConfig = {
    title: "Dashboard Filters",
    layout: "inline",
    showReset: false,
    submitText: "Apply Filters",
    fields: [
      {
        name: "dateRange",
        label: "Date Range",
        type: FormFieldType.DATE_RANGE,
        gridProps: { xs: 24, sm: 12, md: 8 },
      },
      {
        name: "category",
        label: "Category",
        type: FormFieldType.SELECT,
        options: [
          { label: "All Categories", value: "all" },
          { label: "Users", value: "users" },
          { label: "Orders", value: "orders" },
          { label: "Revenue", value: "revenue" },
        ],
        defaultValue: "all",
        gridProps: { xs: 24, sm: 12, md: 8 },
      },
      {
        name: "status",
        label: "Status",
        type: FormFieldType.SELECT,
        options: [
          { label: "All Status", value: "all" },
          { label: "Active", value: "active" },
          { label: "Inactive", value: "inactive" },
        ],
        defaultValue: "all",
        gridProps: { xs: 24, sm: 12, md: 8 },
      },
    ],
    onSubmit: async (values) => {
      console.log("Filter values:", values);
      // Apply filters and reload data
      await loadDashboardData();
    },
  };

  // Responsive grid configuration
  const getColSpan = () => {
    if (isMobile) return 24;
    if (isTablet) return 12;
    return 6;
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <Title level={2} className="m-0">
            <FontAwesomeIcon icon={ICONS.DASHBOARD} className="mr-2" />
            Dashboard
          </Title>
          <Text type="secondary">
            Welcome back! Here's what's happening with your application.
          </Text>
        </div>
        <Space className="mt-4 sm:mt-0">
          <Button type="primary" onClick={loadDashboardData} loading={loading}>
            <FontAwesomeIcon icon={ICONS.REFRESH} className="mr-1" />
            Refresh
          </Button>
        </Space>
      </div>

      {/* Filters */}
      <Card>
        <CustomForm config={filterFormConfig} loading={loading} />
      </Card>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]}>
        <Col span={getColSpan()}>
          <Card>
            <Statistic
              title="Total Users"
              value={stats?.totalUsers || 0}
              precision={0}
              valueStyle={{ color: "#3f8600" }}
              prefix={<UserOutlined />}
              suffix={
                <span className="text-sm">
                  <ArrowUpOutlined className="text-green-500" />
                  {stats?.userGrowth || 0}%
                </span>
              }
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={getColSpan()}>
          <Card>
            <Statistic
              title="Total Orders"
              value={stats?.totalOrders || 0}
              precision={0}
              valueStyle={{
                color: stats && stats.orderGrowth < 0 ? "#cf1322" : "#3f8600",
              }}
              prefix={<ShoppingCartOutlined />}
              suffix={
                <span className="text-sm">
                  {stats && stats.orderGrowth < 0 ? (
                    <ArrowDownOutlined className="text-red-500" />
                  ) : (
                    <ArrowUpOutlined className="text-green-500" />
                  )}
                  {Math.abs(stats?.orderGrowth || 0)}%
                </span>
              }
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={getColSpan()}>
          <Card>
            <Statistic
              title="Total Revenue"
              value={stats?.totalRevenue || 0}
              precision={2}
              valueStyle={{ color: "#3f8600" }}
              prefix={<DollarOutlined />}
              suffix={
                <span className="text-sm">
                  <ArrowUpOutlined className="text-green-500" />
                  {stats?.revenueGrowth || 0}%
                </span>
              }
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={getColSpan()}>
          <Card>
            <Statistic
              title="Page Views"
              value={stats?.totalViews || 0}
              precision={0}
              valueStyle={{ color: "#3f8600" }}
              prefix={<EyeOutlined />}
              suffix={
                <span className="text-sm">
                  <ArrowUpOutlined className="text-green-500" />
                  {stats?.viewGrowth || 0}%
                </span>
              }
              loading={loading}
            />
          </Card>
        </Col>
      </Row>

      {/* Charts and Tables */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          {/* Recent Activities Table using AG Grid */}
          <DataTable
            config={activitiesTableConfig}
            title="Recent Activities"
            useAgGrid={true}
            height={300}
            enableSearch={true}
            enableExport={true}
            enableRefresh={true}
            onRefresh={loadDashboardData}
          />
        </Col>
        <Col xs={24} lg={8}>
          <Card title="Performance Overview" className="h-full">
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <Text>User Engagement</Text>
                  <Text>75%</Text>
                </div>
                <Progress percent={75} status="active" />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <Text>System Performance</Text>
                  <Text>92%</Text>
                </div>
                <Progress percent={92} status="active" strokeColor="#52c41a" />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <Text>Storage Usage</Text>
                  <Text>68%</Text>
                </div>
                <Progress percent={68} status="active" strokeColor="#faad14" />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <Text>Network Latency</Text>
                  <Text>45%</Text>
                </div>
                <Progress percent={45} status="active" strokeColor="#1890ff" />
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
}
