import React, { useState } from 'react';
import { Navigate, useLocation } from 'react-router';
import { Card, Typography, Space, Alert } from 'antd';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { CustomForm } from '@/components/forms/CustomForm';
import { useAuth } from '@/hooks';
import { FormConfig, FormFieldType, LoginCredentials } from '@/types';
import { ICONS } from '@/config/icons';
import { navigation } from '@/config/routes';

/**
 * Login page with CustomForm integration
 * Secure authentication using HTTP-only cookies
 */

const { Title, Text } = Typography;

export default function Login() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { login, isAuthenticated } = useAuth();
  const location = useLocation();

  // Redirect if already authenticated
  if (isAuthenticated) {
    const from = (location.state as any)?.from || navigation.dashboard();
    return <Navigate to={from} replace />;
  }

  // Login form configuration
  const loginFormConfig: FormConfig = {
    title: 'Welcome Back',
    description: 'Please sign in to your account',
    layout: 'vertical',
    submitText: 'Sign In',
    showReset: false,
    fields: [
      {
        name: 'email',
        label: 'Email Address',
        type: FormFieldType.EMAIL,
        required: true,
        placeholder: 'Enter your email address',
        validation: [
          {
            type: 'required',
            message: 'Email is required'
          },
          {
            type: 'pattern',
            value: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$',
            message: 'Please enter a valid email address'
          }
        ],
        gridProps: { xs: 24 }
      },
      {
        name: 'password',
        label: 'Password',
        type: FormFieldType.PASSWORD,
        required: true,
        placeholder: 'Enter your password',
        validation: [
          {
            type: 'required',
            message: 'Password is required'
          },
          {
            type: 'min',
            value: 6,
            message: 'Password must be at least 6 characters'
          }
        ],
        gridProps: { xs: 24 }
      },
      {
        name: 'rememberMe',
        label: 'Remember me',
        type: FormFieldType.CHECKBOX,
        defaultValue: false,
        gridProps: { xs: 24 }
      }
    ],
    onSubmit: handleLogin
  };

  async function handleLogin(values: Record<string, unknown>) {
    try {
      setLoading(true);
      setError(null);

      const credentials: LoginCredentials = {
        email: values.email as string,
        password: values.password as string,
        rememberMe: values.rememberMe as boolean
      };

      await login(credentials);
      
      // Navigation will be handled by the redirect logic above
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Login failed';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100">
            <FontAwesomeIcon 
              icon={ICONS.LOCK} 
              className="h-6 w-6 text-primary-600" 
            />
          </div>
          <Title level={2} className="mt-6 text-center text-gray-900">
            CF Admin Panel
          </Title>
          <Text type="secondary" className="mt-2 text-center">
            Secure access to your admin dashboard
          </Text>
        </div>

        {/* Login Form */}
        <Card className="shadow-lg">
          {error && (
            <Alert
              message="Login Failed"
              description={error}
              type="error"
              showIcon
              closable
              onClose={() => setError(null)}
              className="mb-6"
            />
          )}

          <CustomForm
            config={loginFormConfig}
            loading={loading}
          />

          {/* Additional Links */}
          <div className="mt-6 text-center">
            <Space direction="vertical" size="small">
              <Text type="secondary">
                Forgot your password?{' '}
                <a href="#" className="text-primary-600 hover:text-primary-500">
                  Reset it here
                </a>
              </Text>
              <Text type="secondary" className="text-xs">
                This is a secure area. All access is logged and monitored.
              </Text>
            </Space>
          </div>
        </Card>

        {/* Demo Credentials */}
        {process.env.NODE_ENV === 'development' && (
          <Card className="bg-yellow-50 border-yellow-200">
            <Title level={5} className="text-yellow-800 mb-2">
              Demo Credentials
            </Title>
            <Space direction="vertical" size="small" className="w-full">
              <Text className="text-yellow-700">
                <strong>Admin:</strong> <EMAIL> / admin123
              </Text>
              <Text className="text-yellow-700">
                <strong>User:</strong> <EMAIL> / user123
              </Text>
            </Space>
          </Card>
        )}

        {/* Footer */}
        <div className="text-center">
          <Text type="secondary" className="text-xs">
            © 2024 CF Admin Panel. All rights reserved.
          </Text>
        </div>
      </div>
    </div>
  );
}
