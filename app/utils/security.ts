/**
 * Security utility functions and helpers
 * HTTPS-only configuration, input validation, and security best practices
 */

import { SECURITY, REGEX_PATTERNS } from "@/constants";

/**
 * Security configuration and validation utilities
 */
export class SecurityUtils {
  /**
   * Validate that the application is running over HTTPS in production
   */
  static validateHTTPS(): void {
    if (SECURITY.HTTPS_ONLY && process.env.NODE_ENV === "production") {
      if (window.location.protocol !== "https:") {
        // Redirect to HTTPS
        window.location.href = window.location.href.replace("http:", "https:");
        return;
      }
    }
  }

  /**
   * Generate Content Security Policy header value
   */
  static generateCSP(): string {
    const csp = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Note: Consider removing unsafe-* in production
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https:",
      "connect-src 'self' https:",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'",
    ];
    return csp.join("; ");
  }

  /**
   * Sanitize HTML content to prevent XSS
   */
  static sanitizeHTML(html: string): string {
    const div = document.createElement("div");
    div.textContent = html;
    return div.innerHTML;
  }

  /**
   * Validate and sanitize user input
   */
  static sanitizeInput(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, "") // Remove potential HTML tags
      .replace(/javascript:/gi, "") // Remove javascript: protocol
      .replace(/on\w+=/gi, ""); // Remove event handlers
  }

  /**
   * Validate email format
   */
  static validateEmail(email: string): boolean {
    return REGEX_PATTERNS.EMAIL.test(email);
  }

  /**
   * Validate password strength
   */
  static validatePassword(password: string): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push("Password must be at least 8 characters long");
    }

    if (!/[a-z]/.test(password)) {
      errors.push("Password must contain at least one lowercase letter");
    }

    if (!/[A-Z]/.test(password)) {
      errors.push("Password must contain at least one uppercase letter");
    }

    if (!/\d/.test(password)) {
      errors.push("Password must contain at least one number");
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push("Password must contain at least one special character");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Generate secure random string
   */
  static generateSecureRandom(length: number = 32): string {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, "0")).join(
      ""
    );
  }

  /**
   * Hash sensitive data (client-side hashing for additional security)
   */
  static async hashData(data: string): Promise<string> {
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    const hashBuffer = await crypto.subtle.digest("SHA-256", dataBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map((b) => b.toString(16).padStart(2, "0")).join("");
  }

  /**
   * Validate URL to prevent open redirect attacks
   */
  static validateRedirectURL(url: string): boolean {
    try {
      const urlObj = new URL(url, window.location.origin);
      // Only allow same-origin redirects
      return urlObj.origin === window.location.origin;
    } catch {
      return false;
    }
  }

  /**
   * Check if the current session is secure
   */
  static isSecureContext(): boolean {
    return window.isSecureContext || window.location.protocol === "https:";
  }

  /**
   * Prevent clickjacking by checking if page is in iframe
   */
  static preventClickjacking(): void {
    if (window.top !== window.self) {
      // Page is in an iframe, redirect to top level
      window.top!.location.href = window.location.href;
    }
  }

  /**
   * Rate limiting helper (client-side)
   */
  static createRateLimiter(maxAttempts: number, windowMs: number) {
    const attempts = new Map<string, number[]>();

    return (key: string): boolean => {
      const now = Date.now();
      const userAttempts = attempts.get(key) || [];

      // Remove old attempts outside the window
      const validAttempts = userAttempts.filter(
        (time) => now - time < windowMs
      );

      if (validAttempts.length >= maxAttempts) {
        return false; // Rate limit exceeded
      }

      validAttempts.push(now);
      attempts.set(key, validAttempts);
      return true;
    };
  }

  /**
   * Secure localStorage wrapper that encrypts data
   */
  static secureStorage = {
    async setItem(key: string, value: string): Promise<void> {
      try {
        const encrypted = await this.encrypt(value);
        localStorage.setItem(key, encrypted);
      } catch (error) {
        console.error("Failed to store encrypted data:", error);
      }
    },

    async getItem(key: string): Promise<string | null> {
      try {
        const encrypted = localStorage.getItem(key);
        if (!encrypted) return null;
        return await this.decrypt(encrypted);
      } catch (error) {
        console.error("Failed to retrieve encrypted data:", error);
        return null;
      }
    },

    removeItem(key: string): void {
      localStorage.removeItem(key);
    },

    async encrypt(data: string): Promise<string> {
      // Simple encryption using Web Crypto API
      // In production, use a more robust encryption method
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);
      const key = await crypto.subtle.generateKey(
        { name: "AES-GCM", length: 256 },
        false,
        ["encrypt"]
      );
      const iv = crypto.getRandomValues(new Uint8Array(12));
      const encrypted = await crypto.subtle.encrypt(
        { name: "AES-GCM", iv },
        key,
        dataBuffer
      );

      // Combine IV and encrypted data
      const combined = new Uint8Array(iv.length + encrypted.byteLength);
      combined.set(iv);
      combined.set(new Uint8Array(encrypted), iv.length);

      return btoa(String.fromCharCode(...combined));
    },

    async decrypt(_encryptedData: string): Promise<string> {
      // This is a simplified implementation
      // In production, you'd need to store and retrieve the key securely
      throw new Error(
        "Decryption not implemented - use server-side encryption instead"
      );
    },
  };

  /**
   * CSRF token management
   */
  static csrfToken = {
    get(): string | null {
      const meta = document.querySelector(
        'meta[name="csrf-token"]'
      ) as HTMLMetaElement;
      return meta?.content || null;
    },

    set(token: string): void {
      let meta = document.querySelector(
        'meta[name="csrf-token"]'
      ) as HTMLMetaElement;
      if (!meta) {
        meta = document.createElement("meta");
        meta.name = "csrf-token";
        document.head.appendChild(meta);
      }
      meta.content = token;
    },

    getHeaders(): Record<string, string> {
      const token = this.get();
      return token ? { "X-CSRF-Token": token } : {};
    },
  };

  /**
   * Security headers validation
   */
  static validateSecurityHeaders(response: Response): void {
    const requiredHeaders = {
      "X-Content-Type-Options": "nosniff",
      "X-Frame-Options": ["DENY", "SAMEORIGIN"],
      "X-XSS-Protection": "1; mode=block",
      "Strict-Transport-Security": true, // Just check presence
      "Content-Security-Policy": true,
    };

    Object.entries(requiredHeaders).forEach(([header, expectedValue]) => {
      const actualValue = response.headers.get(header);

      if (!actualValue) {
        console.warn(`Missing security header: ${header}`);
        return;
      }

      if (typeof expectedValue === "string" && actualValue !== expectedValue) {
        console.warn(
          `Incorrect security header value for ${header}: expected ${expectedValue}, got ${actualValue}`
        );
      } else if (
        Array.isArray(expectedValue) &&
        !expectedValue.includes(actualValue)
      ) {
        console.warn(
          `Incorrect security header value for ${header}: expected one of ${expectedValue.join(
            ", "
          )}, got ${actualValue}`
        );
      }
    });
  }

  /**
   * Initialize security measures
   */
  static initialize(): void {
    // Validate HTTPS
    this.validateHTTPS();

    // Prevent clickjacking
    this.preventClickjacking();

    // Set up CSP if not already set by server
    if (!document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
      const meta = document.createElement("meta");
      meta.httpEquiv = "Content-Security-Policy";
      meta.content = this.generateCSP();
      document.head.appendChild(meta);
    }

    // Disable right-click context menu in production (optional)
    if (process.env.NODE_ENV === "production") {
      document.addEventListener("contextmenu", (e) => {
        e.preventDefault();
      });
    }

    // Disable F12 and other developer tools shortcuts in production (optional)
    if (process.env.NODE_ENV === "production") {
      document.addEventListener("keydown", (e) => {
        if (
          e.key === "F12" ||
          (e.ctrlKey && e.shiftKey && e.key === "I") ||
          (e.ctrlKey && e.shiftKey && e.key === "C") ||
          (e.ctrlKey && e.key === "U")
        ) {
          e.preventDefault();
        }
      });
    }
  }
}

// Initialize security measures when module loads
if (typeof window !== "undefined") {
  SecurityUtils.initialize();
}

export default SecurityUtils;
