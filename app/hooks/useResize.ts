import { useState, useEffect, useCallback, useRef } from "react";
import { debounce } from "throttle-debounce";
import { BREAKPOINTS } from "@/constants";

/**
 * Responsive design hook that tracks window resize events with debouncing
 * Returns current viewport dimensions and breakpoint information
 */

// Local type definitions
interface Breakpoint {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  xxl: number;
}

interface ViewportSize {
  width: number;
  height: number;
  breakpoint: keyof Breakpoint;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
}

interface UseResizeOptions {
  debounceMs?: number;
  breakpoints?: Breakpoint;
  enableOrientationChange?: boolean;
}

interface UseResizeReturn extends ViewportSize {
  isLandscape: boolean;
  isPortrait: boolean;
  aspectRatio: number;
  previousSize: ViewportSize | null;
}

export function useResize(options: UseResizeOptions = {}): UseResizeReturn {
  const {
    debounceMs = 150,
    breakpoints = BREAKPOINTS,
    enableOrientationChange = true,
  } = options;

  const [size, setSize] = useState<ViewportSize>(() =>
    getViewportSize(breakpoints)
  );
  const [previousSize, setPreviousSize] = useState<ViewportSize | null>(null);
  const debouncedResizeRef = useRef<ReturnType<typeof debounce> | null>(null);

  const handleResize = useCallback(() => {
    const newSize = getViewportSize(breakpoints);
    setPreviousSize(size);
    setSize(newSize);
  }, [breakpoints, size]);

  useEffect(() => {
    // Create debounced resize handler
    debouncedResizeRef.current = debounce(debounceMs, handleResize);

    // Add event listeners
    window.addEventListener("resize", debouncedResizeRef.current);

    if (enableOrientationChange) {
      window.addEventListener("orientationchange", debouncedResizeRef.current);
    }

    // Initial size calculation
    const initialSize = getViewportSize(breakpoints);
    setSize(initialSize);

    // Cleanup function
    return () => {
      if (debouncedResizeRef.current) {
        window.removeEventListener("resize", debouncedResizeRef.current);

        if (enableOrientationChange) {
          window.removeEventListener(
            "orientationchange",
            debouncedResizeRef.current
          );
        }

        // Cancel any pending debounced calls
        debouncedResizeRef.current.cancel();
      }
    };
  }, [debounceMs, enableOrientationChange, handleResize, breakpoints]);

  // Calculate additional properties
  const isLandscape = size.width > size.height;
  const isPortrait = size.height > size.width;
  const aspectRatio = size.width / size.height;

  return {
    ...size,
    isLandscape,
    isPortrait,
    aspectRatio,
    previousSize,
  };
}

/**
 * Get current viewport size and breakpoint information
 */
function getViewportSize(breakpoints: Breakpoint): ViewportSize {
  // Default values for SSR
  if (typeof window === "undefined") {
    return {
      width: 1200,
      height: 800,
      breakpoint: "lg",
      isMobile: false,
      isTablet: false,
      isDesktop: true,
    };
  }

  const width = window.innerWidth;
  const height = window.innerHeight;

  // Determine current breakpoint
  let breakpoint: keyof Breakpoint = "xs";
  if (width >= breakpoints.xxl) {
    breakpoint = "xxl";
  } else if (width >= breakpoints.xl) {
    breakpoint = "xl";
  } else if (width >= breakpoints.lg) {
    breakpoint = "lg";
  } else if (width >= breakpoints.md) {
    breakpoint = "md";
  } else if (width >= breakpoints.sm) {
    breakpoint = "sm";
  }

  // Determine device type
  const isMobile = width < breakpoints.md;
  const isTablet = width >= breakpoints.md && width < breakpoints.lg;
  const isDesktop = width >= breakpoints.lg;

  return {
    width,
    height,
    breakpoint,
    isMobile,
    isTablet,
    isDesktop,
  };
}

/**
 * Hook to track specific breakpoint matches
 */
export function useBreakpoint(
  breakpoint: keyof Breakpoint,
  breakpoints: Breakpoint = BREAKPOINTS
): boolean {
  const { width } = useResize({ breakpoints });
  return width >= breakpoints[breakpoint];
}

/**
 * Hook to track if viewport is within a range of breakpoints
 */
export function useBreakpointRange(
  min: keyof Breakpoint,
  max: keyof Breakpoint,
  breakpoints: Breakpoint = BREAKPOINTS
): boolean {
  const { width } = useResize({ breakpoints });
  return width >= breakpoints[min] && width < breakpoints[max];
}

/**
 * Hook to get media query string for a breakpoint
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState<boolean>(() => {
    if (typeof window === "undefined") return false;
    return window.matchMedia(query).matches;
  });

  useEffect(() => {
    if (typeof window === "undefined") return;

    const mediaQuery = window.matchMedia(query);
    const handler = (event: MediaQueryListEvent) => setMatches(event.matches);

    // Set initial value
    setMatches(mediaQuery.matches);

    // Add listener
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener("change", handler);
    } else {
      // Fallback for older browsers
      mediaQuery.addListener(handler);
    }

    // Cleanup
    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener("change", handler);
      } else {
        // Fallback for older browsers
        mediaQuery.removeListener(handler);
      }
    };
  }, [query]);

  return matches;
}

/**
 * Hook to track container size (not just viewport)
 */
export function useContainerSize<T extends HTMLElement = HTMLDivElement>(): [
  React.RefObject<T>,
  { width: number; height: number }
] {
  const ref = useRef<T>(null);
  const [size, setSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    if (!ref.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setSize({ width, height });
      }
    });

    resizeObserver.observe(ref.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return [ref, size];
}

/**
 * Hook to track element visibility in viewport
 */
export function useIntersectionObserver<T extends HTMLElement = HTMLDivElement>(
  options: IntersectionObserverInit = {}
): [React.RefObject<T>, boolean] {
  const ref = useRef<T>(null);
  const [isIntersecting, setIsIntersecting] = useState(false);

  useEffect(() => {
    if (!ref.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      {
        threshold: 0.1,
        ...options,
      }
    );

    observer.observe(ref.current);

    return () => {
      observer.disconnect();
    };
  }, [options]);

  return [ref, isIntersecting];
}
