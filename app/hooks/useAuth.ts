import React, {
  useState,
  useEffect,
  useCallback,
  useContext,
  createContext,
} from "react";
import { useHttp } from "./useHttp";
import { AUTH_REFRESH_INTERVAL, SESSION_TIMEOUT } from "@/constants";

// Local type definitions
enum UserRole {
  SUPER_ADMIN = "super_admin",
  ADMIN = "admin",
  MANAGER = "manager",
  USER = "user",
  VIEWER = "viewer",
}

enum Permission {
  READ_USERS = "read_users",
  WRITE_USERS = "write_users",
  DELETE_USERS = "delete_users",
  READ_SETTINGS = "read_settings",
  WRITE_SETTINGS = "write_settings",
  READ_REPORTS = "read_reports",
  WRITE_REPORTS = "write_reports",
  ADMIN_ACCESS = "admin_access",
}

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  permissions: Permission[];
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

/**
 * Authentication state management hook
 * Relies exclusively on HTTP-only secure cookies
 * No localStorage or sessionStorage usage for security
 */

interface AuthContextValue extends AuthState {
  login: (credentials: LoginCredentials) => Promise<User>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  checkAuth: () => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  isSessionExpired: () => boolean;
}

// Create Auth Context
const AuthContext = createContext<AuthContextValue | null>(null);

// Auth Provider Component
interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
  });

  const { execute: httpExecute } = useHttp<User>();
  const { execute: httpExecuteVoid } = useHttp<void>();

  // Check authentication status on mount
  useEffect(() => {
    checkAuth();
  }, []);

  // Set up automatic auth refresh
  useEffect(() => {
    if (!authState.isAuthenticated) return;

    const interval = setInterval(() => {
      refreshAuth().catch(() => {
        // Silent fail - user will be logged out on next API call
      });
    }, AUTH_REFRESH_INTERVAL);

    return () => clearInterval(interval);
  }, [authState.isAuthenticated]);

  const checkAuth = useCallback(async (): Promise<void> => {
    try {
      setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));

      const user = await httpExecute("/api/auth/me");

      setAuthState({
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error:
          error instanceof Error
            ? error.message
            : "Authentication check failed",
      });
    }
  }, [httpExecute]);

  const login = useCallback(
    async (credentials: LoginCredentials): Promise<User> => {
      try {
        setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));

        const user = await httpExecute("/api/auth/login", {
          method: "POST",
          body: credentials,
        });

        setAuthState({
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });

        return user;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Login failed";
        setAuthState((prev) => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));
        throw error;
      }
    },
    [httpExecute]
  );

  const logout = useCallback(async (): Promise<void> => {
    try {
      setAuthState((prev) => ({ ...prev, isLoading: true }));

      await httpExecuteVoid("/api/auth/logout", {
        method: "POST",
      });
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn("Logout API call failed:", error);
    } finally {
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
    }
  }, [httpExecuteVoid]);

  const refreshAuth = useCallback(async (): Promise<void> => {
    try {
      const user = await httpExecute("/api/auth/refresh", {
        method: "POST",
      });

      setAuthState((prev) => ({
        ...prev,
        user,
        isAuthenticated: true,
        error: null,
      }));
    } catch (error) {
      // If refresh fails, user needs to log in again
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: "Session expired. Please log in again.",
      });
      throw error;
    }
  }, [httpExecute]);

  const updateUser = useCallback((userData: Partial<User>): void => {
    setAuthState((prev) => ({
      ...prev,
      user: prev.user ? { ...prev.user, ...userData } : null,
    }));
  }, []);

  const hasPermission = useCallback(
    (permission: string): boolean => {
      if (!authState.user || !authState.isAuthenticated) return false;
      return authState.user.permissions.some((p) => p === permission);
    },
    [authState.user, authState.isAuthenticated]
  );

  const hasRole = useCallback(
    (role: string): boolean => {
      if (!authState.user || !authState.isAuthenticated) return false;
      return authState.user.role === role;
    },
    [authState.user, authState.isAuthenticated]
  );

  const isSessionExpired = useCallback((): boolean => {
    if (!authState.user || !authState.isAuthenticated) return true;

    const lastLogin = authState.user.lastLogin;
    if (!lastLogin) return false;

    const lastLoginTime = new Date(lastLogin).getTime();
    const currentTime = Date.now();

    return currentTime - lastLoginTime > SESSION_TIMEOUT;
  }, [authState.user, authState.isAuthenticated]);

  const contextValue: AuthContextValue = {
    ...authState,
    login,
    logout,
    refreshAuth,
    checkAuth,
    updateUser,
    hasPermission,
    hasRole,
    isSessionExpired,
  };

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth(): AuthContextValue {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }

  return context;
}

// Hook for protected routes
export function useRequireAuth(
  requiredPermissions?: string[],
  requiredRole?: string
) {
  const auth = useAuth();

  useEffect(() => {
    if (!auth.isAuthenticated && !auth.isLoading) {
      // Redirect to login or show unauthorized message
      throw new Error("Authentication required");
    }

    if (requiredRole && !auth.hasRole(requiredRole)) {
      throw new Error("Insufficient role permissions");
    }

    if (
      requiredPermissions &&
      !requiredPermissions.every((permission) => auth.hasPermission(permission))
    ) {
      throw new Error("Insufficient permissions");
    }
  }, [auth, requiredPermissions, requiredRole]);

  return auth;
}

// Hook for conditional rendering based on permissions
export function usePermissions() {
  const { hasPermission, hasRole, user, isAuthenticated } = useAuth();

  const canAccess = useCallback(
    (permissions: string[] = [], roles: string[] = []): boolean => {
      if (!isAuthenticated || !user) return false;

      const hasRequiredPermissions =
        permissions.length === 0 ||
        permissions.some((permission) => hasPermission(permission));
      const hasRequiredRole =
        roles.length === 0 || roles.some((role) => hasRole(role));

      return hasRequiredPermissions && hasRequiredRole;
    },
    [hasPermission, hasRole, user, isAuthenticated]
  );

  return {
    canAccess,
    hasPermission,
    hasRole,
    isAuthenticated,
    user,
  };
}

// Hook for handling authentication errors
export function useAuthError() {
  const { error, isAuthenticated } = useAuth();

  const isAuthError = useCallback((err: unknown): boolean => {
    if (!err) return false;

    if (typeof err === "object" && err !== null && "status" in err) {
      const status = (err as any).status;
      return status === 401 || status === 403;
    }

    return false;
  }, []);

  const handleAuthError = useCallback(
    (err: unknown): void => {
      if (isAuthError(err) && isAuthenticated) {
        // Force logout on authentication errors
        window.location.href = "/login";
      }
    },
    [isAuthError, isAuthenticated]
  );

  return {
    error,
    isAuthError,
    handleAuthError,
  };
}
