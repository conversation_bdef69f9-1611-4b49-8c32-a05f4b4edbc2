import { useState, useCallback, useRef } from "react";
import { API_BASE_URL, API_TIMEOUT, HTTP_STATUS } from "@/constants";

/**
 * HTTP client abstraction hook using fetch API with security-focused implementation
 * Always sends credentials (HTTP-only cookies) and never exposes tokens to JavaScript
 */

// Local type definitions
interface HttpRequestConfig {
  method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  headers?: Record<string, string>;
  body?: unknown;
  credentials?: RequestCredentials;
  signal?: AbortSignal;
}

interface HttpError {
  message: string;
  status?: number;
  statusText?: string;
  data?: unknown;
}

interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: string;
}

interface UseHttpState<T> {
  data: T | null;
  loading: boolean;
  error: HttpError | null;
}

interface UseHttpReturn<T> {
  data: T | null;
  loading: boolean;
  error: HttpError | null;
  execute: (url: string, config?: HttpRequestConfig) => Promise<T>;
  reset: () => void;
  abort: () => void;
}

export function useHttp<T = unknown>(): UseHttpReturn<T> {
  const [state, setState] = useState<UseHttpState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const abortControllerRef = useRef<AbortController | null>(null);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  const abort = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  const execute = useCallback(
    async (url: string, config: HttpRequestConfig = {}): Promise<T> => {
      // Abort any existing request
      abort();

      // Create new abort controller
      abortControllerRef.current = new AbortController();

      setState((prev) => ({
        ...prev,
        loading: true,
        error: null,
      }));

      try {
        const {
          method = "GET",
          headers = {},
          body,
          credentials = "include", // Always include credentials for HTTP-only cookies
          signal,
        } = config;

        // Construct full URL
        const fullUrl = url.startsWith("http") ? url : `${API_BASE_URL}${url}`;

        // Default headers with security considerations
        const defaultHeaders: Record<string, string> = {
          "Content-Type": "application/json",
          "X-Requested-With": "XMLHttpRequest", // CSRF protection
          ...headers,
        };

        // Prepare request configuration
        const requestConfig: RequestInit = {
          method,
          headers: defaultHeaders,
          credentials,
          signal: signal || abortControllerRef.current.signal,
        };

        // Add body for non-GET requests
        if (body && method !== "GET") {
          requestConfig.body =
            typeof body === "string" ? body : JSON.stringify(body);
        }

        // Set timeout
        const timeoutId = setTimeout(() => {
          if (abortControllerRef.current) {
            abortControllerRef.current.abort();
          }
        }, API_TIMEOUT);

        // Make the request
        const response = await fetch(fullUrl, requestConfig);
        clearTimeout(timeoutId);

        // Check if request was aborted
        if (abortControllerRef.current?.signal.aborted) {
          throw new Error("Request was aborted");
        }

        // Parse response
        let responseData: unknown;
        const contentType = response.headers.get("content-type");

        if (contentType?.includes("application/json")) {
          responseData = await response.json();
        } else {
          responseData = await response.text();
        }

        // Handle HTTP errors
        if (!response.ok) {
          const error: HttpError = {
            message: `HTTP ${response.status}: ${response.statusText}`,
            status: response.status,
            statusText: response.statusText,
            data: responseData,
          };

          setState((prev) => ({
            ...prev,
            loading: false,
            error,
          }));

          throw error;
        }

        // Handle API response format
        let finalData: T;
        if (isApiResponse(responseData)) {
          if (!responseData.success) {
            const error: HttpError = {
              message:
                responseData.error ||
                responseData.message ||
                "API request failed",
              status: response.status,
              statusText: response.statusText,
              data: responseData,
            };

            setState((prev) => ({
              ...prev,
              loading: false,
              error,
            }));

            throw error;
          }
          finalData = responseData.data as T;
        } else {
          finalData = responseData as T;
        }

        setState({
          data: finalData,
          loading: false,
          error: null,
        });

        return finalData;
      } catch (err) {
        const error: HttpError = {
          message:
            err instanceof Error ? err.message : "An unknown error occurred",
          status:
            err instanceof Error && "status" in err
              ? (err as any).status
              : undefined,
          statusText:
            err instanceof Error && "statusText" in err
              ? (err as any).statusText
              : undefined,
          data:
            err instanceof Error && "data" in err
              ? (err as any).data
              : undefined,
        };

        setState((prev) => ({
          ...prev,
          loading: false,
          error,
        }));

        throw error;
      } finally {
        abortControllerRef.current = null;
      }
    },
    [abort]
  );

  return {
    data: state.data,
    loading: state.loading,
    error: state.error,
    execute,
    reset,
    abort,
  };
}

// Type guard to check if response follows ApiResponse format
function isApiResponse(data: unknown): data is ApiResponse {
  return (
    typeof data === "object" &&
    data !== null &&
    "success" in data &&
    typeof (data as any).success === "boolean"
  );
}

// Convenience hooks for specific HTTP methods
export function useGet<T = unknown>() {
  const http = useHttp<T>();

  const get = useCallback(
    (url: string, config?: Omit<HttpRequestConfig, "method">) => {
      return http.execute(url, { ...config, method: "GET" });
    },
    [http]
  );

  return {
    ...http,
    get,
  };
}

export function usePost<T = unknown>() {
  const http = useHttp<T>();

  const post = useCallback(
    (
      url: string,
      data?: unknown,
      config?: Omit<HttpRequestConfig, "method" | "body">
    ) => {
      return http.execute(url, { ...config, method: "POST", body: data });
    },
    [http]
  );

  return {
    ...http,
    post,
  };
}

export function usePut<T = unknown>() {
  const http = useHttp<T>();

  const put = useCallback(
    (
      url: string,
      data?: unknown,
      config?: Omit<HttpRequestConfig, "method" | "body">
    ) => {
      return http.execute(url, { ...config, method: "PUT", body: data });
    },
    [http]
  );

  return {
    ...http,
    put,
  };
}

export function useDelete<T = unknown>() {
  const http = useHttp<T>();

  const del = useCallback(
    (url: string, config?: Omit<HttpRequestConfig, "method">) => {
      return http.execute(url, { ...config, method: "DELETE" });
    },
    [http]
  );

  return {
    ...http,
    delete: del,
  };
}
