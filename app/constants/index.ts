// Global constants, enums, and configuration values

import { Breakpoint, ThemeConfig } from '@/types';

// Application Constants
export const APP_NAME = 'CF Admin Panel';
export const APP_VERSION = '1.0.0';
export const APP_DESCRIPTION = 'Scalable React Admin Panel with TypeScript';

// API Configuration
export const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://api.yourdomain.com' 
  : 'https://localhost:3001';

export const API_TIMEOUT = 30000; // 30 seconds
export const API_RETRY_ATTEMPTS = 3;
export const API_RETRY_DELAY = 1000; // 1 second

// Authentication Constants
export const AUTH_COOKIE_NAME = 'auth_session';
export const AUTH_REFRESH_INTERVAL = 15 * 60 * 1000; // 15 minutes
export const SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours

// Route Constants
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  DASHBOARD: '/dashboard',
  USERS: '/users',
  SETTINGS: '/settings',
  PROFILE: '/profile',
  REPORTS: '/reports',
  NOT_FOUND: '/404',
  UNAUTHORIZED: '/401',
  SERVER_ERROR: '/500'
} as const;

// Obfuscated Routes (can be changed via configuration)
export const OBFUSCATED_ROUTES = {
  ADMIN: '/qwerty-admin-xyz123',
  MANAGEMENT: '/mgmt-secure-portal',
  ANALYTICS: '/data-insights-hub'
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503
} as const;

// Responsive Breakpoints
export const BREAKPOINTS: Breakpoint = {
  xs: 480,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1600
};

// Default Theme Configuration
export const DEFAULT_THEME: ThemeConfig = {
  primaryColor: '#1890ff',
  secondaryColor: '#52c41a',
  backgroundColor: '#ffffff',
  textColor: '#000000d9',
  borderColor: '#d9d9d9',
  borderRadius: 6,
  fontSize: {
    small: 12,
    medium: 14,
    large: 16
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32
  }
};

// Form Validation Messages
export const VALIDATION_MESSAGES = {
  REQUIRED: 'This field is required',
  EMAIL_INVALID: 'Please enter a valid email address',
  PASSWORD_MIN_LENGTH: 'Password must be at least 8 characters long',
  PASSWORD_COMPLEXITY: 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
  PHONE_INVALID: 'Please enter a valid phone number',
  URL_INVALID: 'Please enter a valid URL',
  NUMBER_INVALID: 'Please enter a valid number',
  DATE_INVALID: 'Please enter a valid date',
  FILE_SIZE_EXCEEDED: 'File size exceeds the maximum limit',
  FILE_TYPE_INVALID: 'Invalid file type'
} as const;

// Pagination Constants
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 100,
  SHOW_SIZE_CHANGER: true,
  SHOW_QUICK_JUMPER: true
} as const;

// Table Constants
export const TABLE = {
  DEFAULT_SCROLL_Y: 400,
  ROW_SELECTION_TYPE: 'checkbox',
  LOADING_DELAY: 200,
  EMPTY_TEXT: 'No data available'
} as const;

// File Upload Constants
export const FILE_UPLOAD = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: {
    IMAGES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    DOCUMENTS: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    SPREADSHEETS: ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
    ARCHIVES: ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed']
  },
  CHUNK_SIZE: 1024 * 1024 // 1MB chunks for large file uploads
} as const;

// Date and Time Constants
export const DATE_FORMATS = {
  DISPLAY: 'YYYY-MM-DD',
  DISPLAY_WITH_TIME: 'YYYY-MM-DD HH:mm:ss',
  API: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
  HUMAN_READABLE: 'MMM DD, YYYY',
  HUMAN_READABLE_WITH_TIME: 'MMM DD, YYYY HH:mm'
} as const;

// Notification Constants
export const NOTIFICATION = {
  DURATION: 4.5, // seconds
  PLACEMENT: 'topRight',
  MAX_COUNT: 3
} as const;

// Loading States
export const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error'
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  THEME: 'app_theme',
  LANGUAGE: 'app_language',
  USER_PREFERENCES: 'user_preferences',
  SIDEBAR_COLLAPSED: 'sidebar_collapsed',
  TABLE_SETTINGS: 'table_settings'
} as const;

// Error Codes
export const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
} as const;

// Security Constants
export const SECURITY = {
  HTTPS_ONLY: true,
  SECURE_COOKIES: true,
  SAME_SITE: 'strict' as const,
  CSRF_PROTECTION: true,
  XSS_PROTECTION: true,
  CONTENT_SECURITY_POLICY: true
} as const;

// Feature Flags
export const FEATURES = {
  ENABLE_ANALYTICS: process.env.NODE_ENV === 'production',
  ENABLE_DEBUG_MODE: process.env.NODE_ENV === 'development',
  ENABLE_PERFORMANCE_MONITORING: true,
  ENABLE_ERROR_REPORTING: process.env.NODE_ENV === 'production',
  ENABLE_A11Y_CHECKS: true
} as const;

// Regular Expressions
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^\+?[\d\s\-\(\)]+$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  ALPHANUMERIC: /^[a-zA-Z0-9]+$/,
  NUMERIC: /^\d+$/,
  SLUG: /^[a-z0-9]+(?:-[a-z0-9]+)*$/
} as const;

// Animation Constants
export const ANIMATIONS = {
  DURATION: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500
  },
  EASING: {
    EASE_IN: 'cubic-bezier(0.4, 0, 1, 1)',
    EASE_OUT: 'cubic-bezier(0, 0, 0.2, 1)',
    EASE_IN_OUT: 'cubic-bezier(0.4, 0, 0.2, 1)'
  }
} as const;

// Grid System
export const GRID = {
  COLUMNS: 24,
  GUTTER: {
    HORIZONTAL: 16,
    VERTICAL: 16
  }
} as const;

// Z-Index Layers
export const Z_INDEX = {
  DROPDOWN: 1000,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
  NOTIFICATION: 1080,
  LOADING: 1090
} as const;
