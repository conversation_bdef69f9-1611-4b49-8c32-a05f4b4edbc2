/**
 * API abstractions, HTTP clients, and service integrations
 * Security-focused implementation with HTTP-only cookies
 * No token exposure to JavaScript context
 */

import { ApiResponse, PaginatedResponse, User, LoginCredentials } from '@/types';
import { API_BASE_URL, HTTP_STATUS, SECURITY } from '@/constants';

// Base API configuration
const API_CONFIG = {
  baseURL: API_BASE_URL,
  timeout: 30000,
  credentials: 'include' as RequestCredentials, // Always include HTTP-only cookies
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest', // CSRF protection
    'Accept': 'application/json'
  }
};

// Security headers for all requests
const SECURITY_HEADERS = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin'
};

/**
 * Base API client with security features
 */
class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor() {
    this.baseURL = API_CONFIG.baseURL;
    this.defaultHeaders = {
      ...API_CONFIG.headers,
      ...SECURITY_HEADERS
    };
  }

  /**
   * Make HTTP request with security considerations
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    // Ensure HTTPS in production
    if (SECURITY.HTTPS_ONLY && !this.baseURL.startsWith('https://') && process.env.NODE_ENV === 'production') {
      throw new Error('HTTPS is required in production');
    }

    const url = `${this.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      ...options,
      credentials: API_CONFIG.credentials,
      headers: {
        ...this.defaultHeaders,
        ...options.headers
      }
    };

    try {
      const response = await fetch(url, config);
      
      // Check for security headers in response
      this.validateSecurityHeaders(response);
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  /**
   * Validate security headers in response
   */
  private validateSecurityHeaders(response: Response): void {
    const requiredHeaders = [
      'X-Content-Type-Options',
      'X-Frame-Options',
      'X-XSS-Protection'
    ];

    requiredHeaders.forEach(header => {
      if (!response.headers.get(header)) {
        console.warn(`Missing security header: ${header}`);
      }
    });
  }

  // HTTP methods
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  async put<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  async patch<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

// Create singleton API client instance
export const apiClient = new ApiClient();

/**
 * Authentication API service
 * Uses HTTP-only cookies for session management
 */
export const authApi = {
  /**
   * Login user with credentials
   * Server sets HTTP-only cookie on successful authentication
   */
  async login(credentials: LoginCredentials): Promise<User> {
    const response = await apiClient.post<User>('/auth/login', credentials);
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Login failed');
    }
    return response.data;
  },

  /**
   * Logout user
   * Server clears HTTP-only cookie
   */
  async logout(): Promise<void> {
    await apiClient.post<void>('/auth/logout');
  },

  /**
   * Get current user information
   * Uses HTTP-only cookie for authentication
   */
  async getCurrentUser(): Promise<User> {
    const response = await apiClient.get<User>('/auth/me');
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to get user information');
    }
    return response.data;
  },

  /**
   * Refresh authentication session
   * Server validates and refreshes HTTP-only cookie
   */
  async refreshSession(): Promise<User> {
    const response = await apiClient.post<User>('/auth/refresh');
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Session refresh failed');
    }
    return response.data;
  },

  /**
   * Check if user session is valid
   */
  async validateSession(): Promise<boolean> {
    try {
      await this.getCurrentUser();
      return true;
    } catch {
      return false;
    }
  }
};

/**
 * Users API service
 */
export const usersApi = {
  async getUsers(params?: Record<string, unknown>): Promise<PaginatedResponse<User>> {
    const queryString = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : '';
    const response = await apiClient.get<PaginatedResponse<User>>(`/users${queryString}`);
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch users');
    }
    return response.data;
  },

  async getUser(id: string): Promise<User> {
    const response = await apiClient.get<User>(`/users/${id}`);
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch user');
    }
    return response.data;
  },

  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    const response = await apiClient.post<User>('/users', userData);
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to create user');
    }
    return response.data;
  },

  async updateUser(id: string, userData: Partial<User>): Promise<User> {
    const response = await apiClient.put<User>(`/users/${id}`, userData);
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to update user');
    }
    return response.data;
  },

  async deleteUser(id: string): Promise<void> {
    const response = await apiClient.delete<void>(`/users/${id}`);
    if (!response.success) {
      throw new Error(response.error || 'Failed to delete user');
    }
  }
};

/**
 * Settings API service
 */
export const settingsApi = {
  async getSettings(): Promise<Record<string, unknown>> {
    const response = await apiClient.get<Record<string, unknown>>('/settings');
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch settings');
    }
    return response.data;
  },

  async updateSettings(settings: Record<string, unknown>): Promise<Record<string, unknown>> {
    const response = await apiClient.put<Record<string, unknown>>('/settings', settings);
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to update settings');
    }
    return response.data;
  }
};

/**
 * Reports API service
 */
export const reportsApi = {
  async getDashboardStats(): Promise<Record<string, unknown>> {
    const response = await apiClient.get<Record<string, unknown>>('/reports/dashboard');
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch dashboard stats');
    }
    return response.data;
  },

  async getReportData(reportType: string, params?: Record<string, unknown>): Promise<unknown[]> {
    const queryString = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : '';
    const response = await apiClient.get<unknown[]>(`/reports/${reportType}${queryString}`);
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch report data');
    }
    return response.data;
  }
};

/**
 * File upload API service
 */
export const uploadApi = {
  async uploadFile(file: File, onProgress?: (progress: number) => void): Promise<{ url: string; filename: string }> {
    const formData = new FormData();
    formData.append('file', file);

    // Create XMLHttpRequest for progress tracking
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable && onProgress) {
          const progress = (event.loaded / event.total) * 100;
          onProgress(progress);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            if (response.success) {
              resolve(response.data);
            } else {
              reject(new Error(response.error || 'Upload failed'));
            }
          } catch (error) {
            reject(new Error('Invalid response format'));
          }
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed'));
      });

      xhr.open('POST', `${API_CONFIG.baseURL}/upload`);
      xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
      xhr.withCredentials = true; // Include HTTP-only cookies
      xhr.send(formData);
    });
  }
};

// Export all API services
export const api = {
  auth: authApi,
  users: usersApi,
  settings: settingsApi,
  reports: reportsApi,
  upload: uploadApi
};
