import { library } from '@fortawesome/fontawesome-svg-core';
import {
  faUser,
  faUsers,
  faCog,
  faHome,
  faChartBar,
  faSignOutAlt,
  faSignInAlt,
  faEdit,
  faTrash,
  faPlus,
  faSave,
  faCancel,
  faSearch,
  faFilter,
  faDownload,
  faUpload,
  faRefresh,
  faEye,
  faEyeSlash,
  faBell,
  faEnvelope,
  faPhone,
  faMapMarkerAlt,
  faCalendar,
  faClock,
  faFile,
  faFolder,
  faImage,
  faVideo,
  faMusic,
  faCode,
  faDatabase,
  faServer,
  faCloud,
  faLock,
  faUnlock,
  faKey,
  faShield,
  faExclamationTriangle,
  faInfoCircle,
  faCheckCircle,
  faTimesCircle,
  faQuestionCircle,
  faStar,
  faHeart,
  faThumbsUp,
  faThumbsDown,
  faShare,
  faLink,
  faCopy,
  faPrint,
  faExpand,
  faCompress,
  faArrowUp,
  faArrowDown,
  faArrowLeft,
  faArrowRight,
  faChevronUp,
  faChevronDown,
  faChevronLeft,
  faChevronRight,
  faBars,
  faTimes,
  faEllipsisV,
  faEllipsisH,
  faGripVertical,
  faGripHorizontal
} from '@fortawesome/free-solid-svg-icons';

import {
  faFacebook,
  faTwitter,
  faLinkedin,
  faGithub,
  faGoogle,
  faMicrosoft,
  faApple,
  faAmazon
} from '@fortawesome/free-brands-svg-icons';

import {
  faUser as faUserRegular,
  faEnvelope as faEnvelopeRegular,
  faCalendar as faCalendarRegular,
  faClock as faClockRegular,
  faFile as faFileRegular,
  faFolder as faFolderRegular,
  faStar as faStarRegular,
  faHeart as faHeartRegular,
  faThumbsUp as faThumbsUpRegular,
  faThumbsDown as faThumbsDownRegular
} from '@fortawesome/free-regular-svg-icons';

/**
 * Font Awesome icon system setup
 * Centralized icon library configuration
 * TypeScript support for icon names
 */

// Add solid icons to library
library.add(
  faUser,
  faUsers,
  faCog,
  faHome,
  faChartBar,
  faSignOutAlt,
  faSignInAlt,
  faEdit,
  faTrash,
  faPlus,
  faSave,
  faCancel,
  faSearch,
  faFilter,
  faDownload,
  faUpload,
  faRefresh,
  faEye,
  faEyeSlash,
  faBell,
  faEnvelope,
  faPhone,
  faMapMarkerAlt,
  faCalendar,
  faClock,
  faFile,
  faFolder,
  faImage,
  faVideo,
  faMusic,
  faCode,
  faDatabase,
  faServer,
  faCloud,
  faLock,
  faUnlock,
  faKey,
  faShield,
  faExclamationTriangle,
  faInfoCircle,
  faCheckCircle,
  faTimesCircle,
  faQuestionCircle,
  faStar,
  faHeart,
  faThumbsUp,
  faThumbsDown,
  faShare,
  faLink,
  faCopy,
  faPrint,
  faExpand,
  faCompress,
  faArrowUp,
  faArrowDown,
  faArrowLeft,
  faArrowRight,
  faChevronUp,
  faChevronDown,
  faChevronLeft,
  faChevronRight,
  faBars,
  faTimes,
  faEllipsisV,
  faEllipsisH,
  faGripVertical,
  faGripHorizontal
);

// Add brand icons to library
library.add(
  faFacebook,
  faTwitter,
  faLinkedin,
  faGithub,
  faGoogle,
  faMicrosoft,
  faApple,
  faAmazon
);

// Add regular icons to library
library.add(
  faUserRegular,
  faEnvelopeRegular,
  faCalendarRegular,
  faClockRegular,
  faFileRegular,
  faFolderRegular,
  faStarRegular,
  faHeartRegular,
  faThumbsUpRegular,
  faThumbsDownRegular
);

// Icon name types for TypeScript
export type SolidIconName = 
  | 'user'
  | 'users'
  | 'cog'
  | 'home'
  | 'chart-bar'
  | 'sign-out-alt'
  | 'sign-in-alt'
  | 'edit'
  | 'trash'
  | 'plus'
  | 'save'
  | 'cancel'
  | 'search'
  | 'filter'
  | 'download'
  | 'upload'
  | 'refresh'
  | 'eye'
  | 'eye-slash'
  | 'bell'
  | 'envelope'
  | 'phone'
  | 'map-marker-alt'
  | 'calendar'
  | 'clock'
  | 'file'
  | 'folder'
  | 'image'
  | 'video'
  | 'music'
  | 'code'
  | 'database'
  | 'server'
  | 'cloud'
  | 'lock'
  | 'unlock'
  | 'key'
  | 'shield'
  | 'exclamation-triangle'
  | 'info-circle'
  | 'check-circle'
  | 'times-circle'
  | 'question-circle'
  | 'star'
  | 'heart'
  | 'thumbs-up'
  | 'thumbs-down'
  | 'share'
  | 'link'
  | 'copy'
  | 'print'
  | 'expand'
  | 'compress'
  | 'arrow-up'
  | 'arrow-down'
  | 'arrow-left'
  | 'arrow-right'
  | 'chevron-up'
  | 'chevron-down'
  | 'chevron-left'
  | 'chevron-right'
  | 'bars'
  | 'times'
  | 'ellipsis-v'
  | 'ellipsis-h'
  | 'grip-vertical'
  | 'grip-horizontal';

export type BrandIconName = 
  | 'facebook'
  | 'twitter'
  | 'linkedin'
  | 'github'
  | 'google'
  | 'microsoft'
  | 'apple'
  | 'amazon';

export type RegularIconName = 
  | 'user'
  | 'envelope'
  | 'calendar'
  | 'clock'
  | 'file'
  | 'folder'
  | 'star'
  | 'heart'
  | 'thumbs-up'
  | 'thumbs-down';

// Icon configuration object for easy access
export const ICONS = {
  // Navigation
  HOME: 'home' as SolidIconName,
  DASHBOARD: 'chart-bar' as SolidIconName,
  USERS: 'users' as SolidIconName,
  SETTINGS: 'cog' as SolidIconName,
  
  // Authentication
  LOGIN: 'sign-in-alt' as SolidIconName,
  LOGOUT: 'sign-out-alt' as SolidIconName,
  USER: 'user' as SolidIconName,
  
  // Actions
  EDIT: 'edit' as SolidIconName,
  DELETE: 'trash' as SolidIconName,
  ADD: 'plus' as SolidIconName,
  SAVE: 'save' as SolidIconName,
  CANCEL: 'cancel' as SolidIconName,
  SEARCH: 'search' as SolidIconName,
  FILTER: 'filter' as SolidIconName,
  DOWNLOAD: 'download' as SolidIconName,
  UPLOAD: 'upload' as SolidIconName,
  REFRESH: 'refresh' as SolidIconName,
  
  // Visibility
  SHOW: 'eye' as SolidIconName,
  HIDE: 'eye-slash' as SolidIconName,
  
  // Communication
  NOTIFICATION: 'bell' as SolidIconName,
  EMAIL: 'envelope' as SolidIconName,
  PHONE: 'phone' as SolidIconName,
  
  // Files and Media
  FILE: 'file' as SolidIconName,
  FOLDER: 'folder' as SolidIconName,
  IMAGE: 'image' as SolidIconName,
  VIDEO: 'video' as SolidIconName,
  MUSIC: 'music' as SolidIconName,
  
  // Security
  LOCK: 'lock' as SolidIconName,
  UNLOCK: 'unlock' as SolidIconName,
  KEY: 'key' as SolidIconName,
  SHIELD: 'shield' as SolidIconName,
  
  // Status
  WARNING: 'exclamation-triangle' as SolidIconName,
  INFO: 'info-circle' as SolidIconName,
  SUCCESS: 'check-circle' as SolidIconName,
  ERROR: 'times-circle' as SolidIconName,
  QUESTION: 'question-circle' as SolidIconName,
  
  // Navigation arrows
  UP: 'arrow-up' as SolidIconName,
  DOWN: 'arrow-down' as SolidIconName,
  LEFT: 'arrow-left' as SolidIconName,
  RIGHT: 'arrow-right' as SolidIconName,
  
  // Menu
  MENU: 'bars' as SolidIconName,
  CLOSE: 'times' as SolidIconName,
  MORE_VERTICAL: 'ellipsis-v' as SolidIconName,
  MORE_HORIZONTAL: 'ellipsis-h' as SolidIconName
} as const;

// Icon utility functions
export const getIconName = (iconKey: keyof typeof ICONS): SolidIconName => {
  return ICONS[iconKey];
};

// Icon size mapping
export const ICON_SIZES = {
  xs: 'xs',
  sm: 'sm',
  lg: 'lg',
  '1x': '1x',
  '2x': '2x',
  '3x': '3x',
  '4x': '4x',
  '5x': '5x',
  '6x': '6x',
  '7x': '7x',
  '8x': '8x',
  '9x': '9x',
  '10x': '10x'
} as const;

export type IconSize = keyof typeof ICON_SIZES;
