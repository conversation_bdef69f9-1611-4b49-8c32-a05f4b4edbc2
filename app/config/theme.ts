import type { ThemeConfig } from "antd";
import { DEFAULT_THEME } from "../constants";

/**
 * Ant Design theme configuration with Tailwind CSS integration
 * Centralized theme management for consistent styling
 */

// Local theme interface
interface AppThemeConfig {
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  borderRadius: number;
  fontSize: {
    small: number;
    medium: number;
    large: number;
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
}

// Ant Design theme configuration
export const antdTheme: ThemeConfig = {
  token: {
    // Primary colors
    colorPrimary: DEFAULT_THEME.primaryColor,
    colorSuccess: DEFAULT_THEME.secondaryColor,
    colorWarning: "#faad14",
    colorError: "#ff4d4f",
    colorInfo: "#1890ff",

    // Background colors
    colorBgBase: DEFAULT_THEME.backgroundColor,
    colorBgContainer: "#ffffff",
    colorBgElevated: "#ffffff",
    colorBgLayout: "#f5f5f5",

    // Text colors
    colorText: DEFAULT_THEME.textColor,
    colorTextSecondary: "rgba(0, 0, 0, 0.65)",
    colorTextTertiary: "rgba(0, 0, 0, 0.45)",
    colorTextQuaternary: "rgba(0, 0, 0, 0.25)",

    // Border
    colorBorder: DEFAULT_THEME.borderColor,
    colorBorderSecondary: "#f0f0f0",
    borderRadius: DEFAULT_THEME.borderRadius,

    // Typography
    fontSize: DEFAULT_THEME.fontSize.medium,
    fontSizeSM: DEFAULT_THEME.fontSize.small,
    fontSizeLG: DEFAULT_THEME.fontSize.large,
    fontFamily:
      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',

    // Spacing
    padding: DEFAULT_THEME.spacing.md,
    paddingSM: DEFAULT_THEME.spacing.sm,
    paddingLG: DEFAULT_THEME.spacing.lg,
    paddingXL: DEFAULT_THEME.spacing.xl,

    margin: DEFAULT_THEME.spacing.md,
    marginSM: DEFAULT_THEME.spacing.sm,
    marginLG: DEFAULT_THEME.spacing.lg,
    marginXL: DEFAULT_THEME.spacing.xl,

    // Layout colors are handled in components section

    // Control heights
    controlHeight: 32,
    controlHeightSM: 24,
    controlHeightLG: 40,

    // Motion
    motionDurationFast: "0.1s",
    motionDurationMid: "0.2s",
    motionDurationSlow: "0.3s",

    // Shadows
    boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
    boxShadowSecondary: "0 4px 12px rgba(0, 0, 0, 0.15)",

    // Z-index
    zIndexBase: 0,
    zIndexPopupBase: 1000,

    // Line height
    lineHeight: 1.5715,
    lineHeightLG: 1.5,
    lineHeightSM: 1.66,

    // Screen sizes (for responsive design)
    screenXS: 480,
    screenSM: 576,
    screenMD: 768,
    screenLG: 992,
    screenXL: 1200,
    screenXXL: 1600,
  },

  components: {
    // Button customization
    Button: {
      borderRadius: DEFAULT_THEME.borderRadius,
      controlHeight: 32,
      paddingContentHorizontal: 16,
    },

    // Input customization
    Input: {
      borderRadius: DEFAULT_THEME.borderRadius,
      controlHeight: 32,
      paddingInline: 12,
    },

    // Table customization
    Table: {
      borderRadius: DEFAULT_THEME.borderRadius,
      headerBg: "#fafafa",
      headerColor: DEFAULT_THEME.textColor,
      rowHoverBg: "#f5f5f5",
    },

    // Card customization
    Card: {
      borderRadius: DEFAULT_THEME.borderRadius,
      paddingLG: DEFAULT_THEME.spacing.lg,
    },

    // Menu customization
    Menu: {
      itemBg: "transparent",
      itemSelectedBg: DEFAULT_THEME.primaryColor,
      itemSelectedColor: "#ffffff",
      itemHoverBg: "rgba(24, 144, 255, 0.1)",
      itemHoverColor: DEFAULT_THEME.primaryColor,
    },

    // Layout customization
    Layout: {
      siderBg: "#001529",
      headerBg: "#ffffff",
      bodyBg: "#f5f5f5",
      footerBg: "#ffffff",
    },

    // Form customization
    Form: {
      labelColor: DEFAULT_THEME.textColor,
      labelFontSize: DEFAULT_THEME.fontSize.medium,
      itemMarginBottom: 16,
    },

    // Modal customization
    Modal: {
      borderRadius: DEFAULT_THEME.borderRadius,
      paddingLG: DEFAULT_THEME.spacing.lg,
    },

    // Drawer customization
    Drawer: {
      paddingLG: DEFAULT_THEME.spacing.lg,
    },

    // Notification customization
    Notification: {
      borderRadius: DEFAULT_THEME.borderRadius,
      paddingLG: DEFAULT_THEME.spacing.lg,
    },

    // Message customization
    Message: {
      borderRadius: DEFAULT_THEME.borderRadius,
    },
  },

  algorithm: undefined, // Use default algorithm, can be changed to dark theme algorithm
};

// Dark theme configuration
export const darkTheme: ThemeConfig = {
  ...antdTheme,
  token: {
    ...antdTheme.token,
    colorBgBase: "#141414",
    colorBgContainer: "#1f1f1f",
    colorBgElevated: "#262626",
    colorBgLayout: "#000000",
    colorText: "rgba(255, 255, 255, 0.85)",
    colorTextSecondary: "rgba(255, 255, 255, 0.65)",
    colorTextTertiary: "rgba(255, 255, 255, 0.45)",
    colorTextQuaternary: "rgba(255, 255, 255, 0.25)",
    colorBorder: "#434343",
    colorBorderSecondary: "#303030",
  },
  components: {
    ...antdTheme.components,
    Table: {
      ...antdTheme.components?.Table,
      headerBg: "#1f1f1f",
      headerColor: "rgba(255, 255, 255, 0.85)",
      rowHoverBg: "#262626",
    },
    Layout: {
      ...antdTheme.components?.Layout,
      siderBg: "#001529",
      headerBg: "#1f1f1f",
      bodyBg: "#141414",
      footerBg: "#1f1f1f",
    },
  },
};

// Theme utility functions
export class ThemeManager {
  private static instance: ThemeManager;
  private currentTheme: "light" | "dark" = "light";
  private listeners: Array<(theme: "light" | "dark") => void> = [];

  private constructor() {
    // Initialize theme from localStorage or system preference
    this.initializeTheme();
  }

  public static getInstance(): ThemeManager {
    if (!ThemeManager.instance) {
      ThemeManager.instance = new ThemeManager();
    }
    return ThemeManager.instance;
  }

  private initializeTheme(): void {
    // Check localStorage first
    const savedTheme = localStorage.getItem("app_theme") as "light" | "dark";
    if (savedTheme) {
      this.currentTheme = savedTheme;
      return;
    }

    // Check system preference
    if (
      window.matchMedia &&
      window.matchMedia("(prefers-color-scheme: dark)").matches
    ) {
      this.currentTheme = "dark";
    }
  }

  public getCurrentTheme(): "light" | "dark" {
    return this.currentTheme;
  }

  public getThemeConfig(): ThemeConfig {
    return this.currentTheme === "dark" ? darkTheme : antdTheme;
  }

  public setTheme(theme: "light" | "dark"): void {
    this.currentTheme = theme;
    localStorage.setItem("app_theme", theme);
    this.notifyListeners();
  }

  public toggleTheme(): void {
    this.setTheme(this.currentTheme === "light" ? "dark" : "light");
  }

  public subscribe(listener: (theme: "light" | "dark") => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach((listener) => listener(this.currentTheme));
  }
}

// Export singleton instance
export const themeManager = ThemeManager.getInstance();

// Tailwind CSS integration variables
export const tailwindThemeVars = {
  colors: {
    primary: DEFAULT_THEME.primaryColor,
    secondary: DEFAULT_THEME.secondaryColor,
    background: DEFAULT_THEME.backgroundColor,
    text: DEFAULT_THEME.textColor,
    border: DEFAULT_THEME.borderColor,
  },
  spacing: DEFAULT_THEME.spacing,
  fontSize: DEFAULT_THEME.fontSize,
  borderRadius: {
    default: `${DEFAULT_THEME.borderRadius}px`,
  },
};
