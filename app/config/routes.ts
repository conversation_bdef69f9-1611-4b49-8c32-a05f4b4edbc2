import { RouteConfig } from "@/types";
import { ROUTES, OBFUSCATED_ROUTES } from "@/constants";

/**
 * Centralized route configuration with support for obfuscated/dynamic paths
 * No hardcoded routes anywhere in the application
 * React Router v7 flat routes implementation
 */

// Route configuration interface
export interface AppRouteConfig extends RouteConfig {
  id: string;
  parentId?: string;
  index?: boolean;
  caseSensitive?: boolean;
  children?: AppRouteConfig[];
}

// Environment-based route configuration
const isDevelopment = process.env.NODE_ENV === "development";
const enableObfuscation = !isDevelopment;

// Base route configurations
export const routeConfigs: AppRouteConfig[] = [
  {
    id: "root",
    path: "/",
    component: () => import("@/pages/Layout"),
    title: "CF Admin Panel",
    description: "Main application layout",
    children: [
      {
        id: "home",
        index: true,
        path: ROUTES.HOME,
        component: () => import("@/pages/Home"),
        title: "Dashboard",
        description: "Main dashboard page",
        requiresAuth: true,
        layout: "default",
      },
      {
        id: "login",
        path: ROUTES.LOGIN,
        component: () => import("@/pages/auth/Login"),
        title: "Login",
        description: "User authentication page",
        requiresAuth: false,
        layout: "auth",
      },
      {
        id: "dashboard",
        path: getRoutePath("DASHBOARD"),
        component: () => import("@/pages/Dashboard"),
        title: "Dashboard",
        description: "Main dashboard with analytics",
        requiresAuth: true,
        requiredPermissions: [Permission.READ_REPORTS],
        layout: "default",
      },
      {
        id: "users",
        path: getRoutePath("USERS"),
        component: () => import("@/pages/users/UserList"),
        title: "Users",
        description: "User management",
        requiresAuth: true,
        requiredPermissions: [Permission.READ_USERS],
        layout: "default",
        children: [
          {
            id: "user-detail",
            path: `${getRoutePath("USERS")}/:id`,
            component: () => import("@/pages/users/UserDetail"),
            title: "User Details",
            description: "Individual user details",
            requiresAuth: true,
            requiredPermissions: [Permission.READ_USERS],
          },
          {
            id: "user-create",
            path: `${getRoutePath("USERS")}/create`,
            component: () => import("@/pages/users/UserCreate"),
            title: "Create User",
            description: "Create new user",
            requiresAuth: true,
            requiredPermissions: [Permission.WRITE_USERS],
          },
          {
            id: "user-edit",
            path: `${getRoutePath("USERS")}/:id/edit`,
            component: () => import("@/pages/users/UserEdit"),
            title: "Edit User",
            description: "Edit user details",
            requiresAuth: true,
            requiredPermissions: [Permission.WRITE_USERS],
          },
        ],
      },
      {
        id: "settings",
        path: getRoutePath("SETTINGS"),
        component: () => import("@/pages/settings/Settings"),
        title: "Settings",
        description: "Application settings",
        requiresAuth: true,
        requiredPermissions: [Permission.READ_SETTINGS],
        layout: "default",
        children: [
          {
            id: "general-settings",
            path: `${getRoutePath("SETTINGS")}/general`,
            component: () => import("@/pages/settings/GeneralSettings"),
            title: "General Settings",
            description: "General application settings",
            requiresAuth: true,
            requiredPermissions: [Permission.WRITE_SETTINGS],
          },
          {
            id: "security-settings",
            path: `${getRoutePath("SETTINGS")}/security`,
            component: () => import("@/pages/settings/SecuritySettings"),
            title: "Security Settings",
            description: "Security and authentication settings",
            requiresAuth: true,
            requiredPermissions: [Permission.ADMIN_ACCESS],
          },
        ],
      },
      {
        id: "profile",
        path: ROUTES.PROFILE,
        component: () => import("@/pages/Profile"),
        title: "Profile",
        description: "User profile page",
        requiresAuth: true,
        layout: "default",
      },
      {
        id: "reports",
        path: getRoutePath("REPORTS"),
        component: () => import("@/pages/reports/Reports"),
        title: "Reports",
        description: "Analytics and reporting",
        requiresAuth: true,
        requiredPermissions: [Permission.READ_REPORTS],
        layout: "default",
      },
      // Admin routes with obfuscation
      {
        id: "admin",
        path: enableObfuscation ? OBFUSCATED_ROUTES.ADMIN : "/admin",
        component: () => import("@/pages/admin/AdminPanel"),
        title: "Admin Panel",
        description: "Administrative functions",
        requiresAuth: true,
        requiredPermissions: [Permission.ADMIN_ACCESS],
        layout: "default",
      },
      // Error pages
      {
        id: "not-found",
        path: ROUTES.NOT_FOUND,
        component: () => import("@/pages/errors/NotFound"),
        title: "404 - Page Not Found",
        description: "Page not found error",
        requiresAuth: false,
        layout: "minimal",
      },
      {
        id: "unauthorized",
        path: ROUTES.UNAUTHORIZED,
        component: () => import("@/pages/errors/Unauthorized"),
        title: "401 - Unauthorized",
        description: "Unauthorized access error",
        requiresAuth: false,
        layout: "minimal",
      },
      {
        id: "server-error",
        path: ROUTES.SERVER_ERROR,
        component: () => import("@/pages/errors/ServerError"),
        title: "500 - Server Error",
        description: "Internal server error",
        requiresAuth: false,
        layout: "minimal",
      },
    ],
  },
];

// Helper function to get route path with obfuscation support
function getRoutePath(routeKey: keyof typeof ROUTES): string {
  const basePath = ROUTES[routeKey];

  // Apply obfuscation for specific routes in production
  if (enableObfuscation) {
    switch (routeKey) {
      case "DASHBOARD":
        return "/main-panel";
      case "USERS":
        return "/user-mgmt";
      case "SETTINGS":
        return "/config";
      case "REPORTS":
        return "/analytics";
      default:
        return basePath;
    }
  }

  return basePath;
}

// Route utilities
export class RouteManager {
  private static instance: RouteManager;
  private routeMap: Map<string, AppRouteConfig> = new Map();

  private constructor() {
    this.buildRouteMap(routeConfigs);
  }

  public static getInstance(): RouteManager {
    if (!RouteManager.instance) {
      RouteManager.instance = new RouteManager();
    }
    return RouteManager.instance;
  }

  private buildRouteMap(routes: AppRouteConfig[], parentPath = ""): void {
    routes.forEach((route) => {
      const fullPath = parentPath + route.path;
      this.routeMap.set(route.id, { ...route, path: fullPath });

      if (route.children) {
        this.buildRouteMap(route.children, fullPath);
      }
    });
  }

  public getRoute(id: string): AppRouteConfig | undefined {
    return this.routeMap.get(id);
  }

  public getRoutePath(id: string): string {
    const route = this.routeMap.get(id);
    return route?.path || "/";
  }

  public getAllRoutes(): AppRouteConfig[] {
    return Array.from(this.routeMap.values());
  }

  public getRoutesByPermission(permission: Permission): AppRouteConfig[] {
    return this.getAllRoutes().filter((route) =>
      route.requiredPermissions?.includes(permission)
    );
  }

  public getPublicRoutes(): AppRouteConfig[] {
    return this.getAllRoutes().filter((route) => !route.requiresAuth);
  }

  public getProtectedRoutes(): AppRouteConfig[] {
    return this.getAllRoutes().filter((route) => route.requiresAuth);
  }

  // Dynamic route path modification
  public updateRoutePath(id: string, newPath: string): void {
    const route = this.routeMap.get(id);
    if (route) {
      route.path = newPath;
      route.obfuscatedPath = newPath;
    }
  }

  // Check if user can access route
  public canAccessRoute(
    routeId: string,
    userPermissions: Permission[],
    userRole: UserRole
  ): boolean {
    const route = this.getRoute(routeId);
    if (!route) return false;

    // Check authentication requirement
    if (route.requiresAuth === false) return true;

    // Check permissions
    if (route.requiredPermissions) {
      const hasPermission = route.requiredPermissions.some((permission) =>
        userPermissions.includes(permission)
      );
      if (!hasPermission) return false;
    }

    return true;
  }
}

// Export singleton instance
export const routeManager = RouteManager.getInstance();

// Navigation helpers
export const navigation = {
  home: () => routeManager.getRoutePath("home"),
  dashboard: () => routeManager.getRoutePath("dashboard"),
  users: () => routeManager.getRoutePath("users"),
  userDetail: (id: string) =>
    routeManager.getRoutePath("user-detail").replace(":id", id),
  userCreate: () => routeManager.getRoutePath("user-create"),
  userEdit: (id: string) =>
    routeManager.getRoutePath("user-edit").replace(":id", id),
  settings: () => routeManager.getRoutePath("settings"),
  profile: () => routeManager.getRoutePath("profile"),
  reports: () => routeManager.getRoutePath("reports"),
  admin: () => routeManager.getRoutePath("admin"),
  login: () => routeManager.getRoutePath("login"),
  notFound: () => routeManager.getRoutePath("not-found"),
  unauthorized: () => routeManager.getRoutePath("unauthorized"),
  serverError: () => routeManager.getRoutePath("server-error"),
};

// Route configuration for React Router v7
export function generateReactRouterConfig(): any[] {
  const flattenRoutes = (routes: AppRouteConfig[]): any[] => {
    const result: any[] = [];

    routes.forEach((route) => {
      const routeConfig: any = {
        id: route.id,
        path: route.path,
        lazy: route.component,
        caseSensitive: route.caseSensitive,
      };

      if (route.index) {
        routeConfig.index = true;
      }

      result.push(routeConfig);

      if (route.children) {
        result.push(...flattenRoutes(route.children));
      }
    });

    return result;
  };

  return flattenRoutes(routeConfigs);
}
