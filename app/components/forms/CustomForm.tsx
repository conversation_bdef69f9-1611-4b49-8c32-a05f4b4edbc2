import React, { useCallback, useEffect, useMemo } from 'react';
import {
  Form,
  Input,
  InputNumber,
  Select,
  Checkbox,
  Radio,
  Switch,
  Slider,
  Rate,
  DatePicker,
  TimePicker,
  Upload,
  Button,
  Row,
  Col,
  Typography,
  Space
} from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { FormConfig, FormField, FormFieldType, ValidationRule } from '@/types';
import { VALIDATION_MESSAGES } from '@/constants';

/**
 * Configuration-driven CustomForm component
 * Dynamic field rendering based on JSON configuration
 * Integrated validation with proper TypeScript typing
 * Support for Ant Design form components
 * Extensible field type system
 */

const { TextArea } = Input;
const { Option } = Select;
const { Group: RadioGroup } = Radio;
const { Group: CheckboxGroup } = Checkbox;
const { RangePicker } = DatePicker;
const { Title, Text } = Typography;

interface CustomFormProps {
  config: FormConfig;
  loading?: boolean;
  disabled?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export function CustomForm({
  config,
  loading = false,
  disabled = false,
  className,
  style
}: CustomFormProps) {
  const [form] = Form.useForm();

  // Set initial values
  useEffect(() => {
    if (config.initialValues) {
      form.setFieldsValue(config.initialValues);
    }
  }, [config.initialValues, form]);

  // Handle form submission
  const handleSubmit = useCallback(async (values: Record<string, unknown>) => {
    try {
      if (config.onSubmit) {
        await config.onSubmit(values);
      }
    } catch (error) {
      console.error('Form submission error:', error);
    }
  }, [config.onSubmit]);

  // Handle form reset
  const handleReset = useCallback(() => {
    form.resetFields();
    if (config.onReset) {
      config.onReset();
    }
  }, [form, config.onReset]);

  // Convert validation rules to Ant Design format
  const convertValidationRules = useCallback((rules: ValidationRule[] = []) => {
    return rules.map(rule => {
      switch (rule.type) {
        case 'required':
          return {
            required: true,
            message: rule.message || VALIDATION_MESSAGES.REQUIRED
          };
        case 'min':
          return {
            min: rule.value as number,
            message: rule.message
          };
        case 'max':
          return {
            max: rule.value as number,
            message: rule.message
          };
        case 'pattern':
          return {
            pattern: new RegExp(rule.value as string),
            message: rule.message
          };
        case 'custom':
          return {
            validator: async (_: unknown, value: unknown) => {
              if (rule.validator) {
                const isValid = await rule.validator(value);
                if (!isValid) {
                  throw new Error(rule.message);
                }
              }
            }
          };
        default:
          return {};
      }
    });
  }, []);

  // Check field dependencies
  const checkFieldDependencies = useCallback((field: FormField, formValues: Record<string, unknown>) => {
    if (!field.dependencies || field.dependencies.length === 0) {
      return { show: true, enabled: true };
    }

    let show = true;
    let enabled = true;

    field.dependencies.forEach(dep => {
      const dependentValue = formValues[dep.field];
      let conditionMet = false;

      switch (dep.condition) {
        case 'equals':
          conditionMet = dependentValue === dep.value;
          break;
        case 'not_equals':
          conditionMet = dependentValue !== dep.value;
          break;
        case 'contains':
          conditionMet = Array.isArray(dependentValue) && dependentValue.includes(dep.value);
          break;
        case 'not_contains':
          conditionMet = Array.isArray(dependentValue) && !dependentValue.includes(dep.value);
          break;
      }

      if (conditionMet) {
        switch (dep.action) {
          case 'show':
            show = true;
            break;
          case 'hide':
            show = false;
            break;
          case 'enable':
            enabled = true;
            break;
          case 'disable':
            enabled = false;
            break;
        }
      }
    });

    return { show, enabled };
  }, []);

  // Render individual form field
  const renderField = useCallback((field: FormField, formValues: Record<string, unknown>) => {
    const { show, enabled } = checkFieldDependencies(field, formValues);
    
    if (!show || field.hidden) {
      return null;
    }

    const isDisabled = disabled || field.disabled || !enabled;
    const rules = convertValidationRules(field.validation);

    let fieldComponent: React.ReactNode;

    switch (field.type) {
      case FormFieldType.TEXT:
      case FormFieldType.EMAIL:
      case FormFieldType.PASSWORD:
        fieldComponent = (
          <Input
            placeholder={field.placeholder}
            disabled={isDisabled}
            type={field.type === FormFieldType.PASSWORD ? 'password' : field.type}
          />
        );
        break;

      case FormFieldType.NUMBER:
        fieldComponent = (
          <InputNumber
            placeholder={field.placeholder}
            disabled={isDisabled}
            style={{ width: '100%' }}
          />
        );
        break;

      case FormFieldType.TEXTAREA:
        fieldComponent = (
          <TextArea
            placeholder={field.placeholder}
            disabled={isDisabled}
            rows={4}
          />
        );
        break;

      case FormFieldType.SELECT:
        fieldComponent = (
          <Select
            placeholder={field.placeholder}
            disabled={isDisabled}
            allowClear
          >
            {field.options?.map(option => (
              <Option key={option.value} value={option.value} disabled={option.disabled}>
                {option.label}
              </Option>
            ))}
          </Select>
        );
        break;

      case FormFieldType.MULTI_SELECT:
        fieldComponent = (
          <Select
            mode="multiple"
            placeholder={field.placeholder}
            disabled={isDisabled}
            allowClear
          >
            {field.options?.map(option => (
              <Option key={option.value} value={option.value} disabled={option.disabled}>
                {option.label}
              </Option>
            ))}
          </Select>
        );
        break;

      case FormFieldType.CHECKBOX:
        if (field.options && field.options.length > 1) {
          fieldComponent = (
            <CheckboxGroup
              options={field.options.map(opt => ({
                label: opt.label,
                value: opt.value,
                disabled: opt.disabled || isDisabled
              }))}
              disabled={isDisabled}
            />
          );
        } else {
          fieldComponent = (
            <Checkbox disabled={isDisabled}>
              {field.label}
            </Checkbox>
          );
        }
        break;

      case FormFieldType.RADIO:
        fieldComponent = (
          <RadioGroup disabled={isDisabled}>
            {field.options?.map(option => (
              <Radio key={option.value} value={option.value} disabled={option.disabled}>
                {option.label}
              </Radio>
            ))}
          </RadioGroup>
        );
        break;

      case FormFieldType.SWITCH:
        fieldComponent = (
          <Switch disabled={isDisabled} />
        );
        break;

      case FormFieldType.SLIDER:
        fieldComponent = (
          <Slider disabled={isDisabled} />
        );
        break;

      case FormFieldType.RATE:
        fieldComponent = (
          <Rate disabled={isDisabled} />
        );
        break;

      case FormFieldType.DATE:
        fieldComponent = (
          <DatePicker
            placeholder={field.placeholder}
            disabled={isDisabled}
            style={{ width: '100%' }}
          />
        );
        break;

      case FormFieldType.DATE_RANGE:
        fieldComponent = (
          <RangePicker
            disabled={isDisabled}
            style={{ width: '100%' }}
          />
        );
        break;

      case FormFieldType.TIME:
        fieldComponent = (
          <TimePicker
            placeholder={field.placeholder}
            disabled={isDisabled}
            style={{ width: '100%' }}
          />
        );
        break;

      case FormFieldType.FILE:
        fieldComponent = (
          <Upload
            disabled={isDisabled}
            beforeUpload={() => false} // Prevent auto upload
          >
            <Button icon={<UploadOutlined />} disabled={isDisabled}>
              {field.placeholder || 'Select File'}
            </Button>
          </Upload>
        );
        break;

      default:
        fieldComponent = (
          <Input
            placeholder={field.placeholder}
            disabled={isDisabled}
          />
        );
    }

    const gridProps = field.gridProps || { xs: 24, sm: 12, md: 8, lg: 6 };

    return (
      <Col key={field.name} {...gridProps}>
        <Form.Item
          name={field.name}
          label={field.label}
          rules={rules}
          valuePropName={field.type === FormFieldType.SWITCH || field.type === FormFieldType.CHECKBOX ? 'checked' : 'value'}
        >
          {fieldComponent}
        </Form.Item>
      </Col>
    );
  }, [checkFieldDependencies, convertValidationRules, disabled]);

  // Watch form values for dependencies
  const formValues = Form.useWatch([], form) || {};

  // Memoize rendered fields for performance
  const renderedFields = useMemo(() => {
    return config.fields.map(field => renderField(field, formValues));
  }, [config.fields, renderField, formValues]);

  return (
    <div className={className} style={style}>
      {config.title && (
        <Title level={3} style={{ marginBottom: 16 }}>
          {config.title}
        </Title>
      )}
      
      {config.description && (
        <Text type="secondary" style={{ display: 'block', marginBottom: 24 }}>
          {config.description}
        </Text>
      )}

      <Form
        form={form}
        layout={config.layout || 'vertical'}
        onFinish={handleSubmit}
        disabled={disabled}
        initialValues={config.initialValues}
      >
        <Row gutter={[16, 16]}>
          {renderedFields}
        </Row>

        <Form.Item style={{ marginTop: 24 }}>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              disabled={disabled}
            >
              {config.submitText || 'Submit'}
            </Button>
            
            {config.showReset !== false && (
              <Button
                onClick={handleReset}
                disabled={disabled}
              >
                {config.resetText || 'Reset'}
              </Button>
            )}
          </Space>
        </Form.Item>
      </Form>
    </div>
  );
}
