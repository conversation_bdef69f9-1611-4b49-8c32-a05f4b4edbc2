import React, { useCallback, useMemo } from 'react';
import { AgGridReact } from 'ag-grid-react';
import { ColDef, GridOptions, GridReadyEvent } from 'ag-grid-community';
import { Table, Card, Space, Button, Input, Typography } from 'antd';
import { SearchOutlined, ReloadOutlined, DownloadOutlined } from '@ant-design/icons';
import { TableConfig } from '@/types';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';

/**
 * Configurable DataTable component using AG Grid
 * Supports both AG Grid and Ant Design Table based on preference
 * TypeScript interfaces for data tables with proper configuration
 */

const { Title } = Typography;
const { Search } = Input;

interface DataTableProps<T = unknown> {
  config: TableConfig<T>;
  title?: string;
  useAgGrid?: boolean;
  height?: number;
  enableSearch?: boolean;
  enableExport?: boolean;
  enableRefresh?: boolean;
  onRefresh?: () => void;
  onExport?: () => void;
  className?: string;
  style?: React.CSSProperties;
}

export function DataTable<T extends Record<string, unknown>>({
  config,
  title,
  useAgGrid = true,
  height = 400,
  enableSearch = true,
  enableExport = true,
  enableRefresh = true,
  onRefresh,
  onExport,
  className,
  style
}: DataTableProps<T>) {
  // Convert table config to AG Grid column definitions
  const agGridColumns = useMemo((): ColDef[] => {
    return config.columns.map(col => ({
      field: col.dataIndex || col.key,
      headerName: col.title,
      width: col.width,
      pinned: col.fixed === 'left' ? 'left' : col.fixed === 'right' ? 'right' : undefined,
      sortable: col.sortable !== false,
      filter: col.filterable !== false,
      resizable: true,
      cellRenderer: col.render ? (params: any) => {
        return col.render!(params.value, params.data, params.node.rowIndex);
      } : undefined,
      headerClass: `text-${col.align || 'left'}`
    }));
  }, [config.columns]);

  // AG Grid options
  const gridOptions = useMemo((): GridOptions => ({
    columnDefs: agGridColumns,
    rowData: config.dataSource,
    defaultColDef: {
      sortable: true,
      filter: true,
      resizable: true,
      minWidth: 100
    },
    pagination: !!config.pagination,
    paginationPageSize: config.pagination?.pageSize || 10,
    rowSelection: config.rowSelection?.type || 'single',
    suppressRowClickSelection: false,
    animateRows: true,
    enableRangeSelection: true,
    suppressMenuHide: true,
    onSelectionChanged: (event) => {
      if (config.rowSelection?.onChange) {
        const selectedRows = event.api.getSelectedRows();
        const selectedKeys = selectedRows.map((row, index) => index);
        config.rowSelection.onChange(selectedKeys, selectedRows);
      }
    }
  }), [agGridColumns, config]);

  // Handle grid ready
  const onGridReady = useCallback((params: GridReadyEvent) => {
    params.api.sizeColumnsToFit();
  }, []);

  // Handle search
  const handleSearch = useCallback((value: string) => {
    // Implementation depends on whether using AG Grid or Ant Table
    if (useAgGrid) {
      // AG Grid global filter would be implemented here
      console.log('AG Grid search:', value);
    }
  }, [useAgGrid]);

  // Handle export
  const handleExport = useCallback(() => {
    if (onExport) {
      onExport();
    } else {
      // Default export functionality
      console.log('Export data');
    }
  }, [onExport]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    if (onRefresh) {
      onRefresh();
    }
  }, [onRefresh]);

  // Render toolbar
  const renderToolbar = () => (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'space-between', 
      alignItems: 'center',
      marginBottom: 16 
    }}>
      <div>
        {title && <Title level={4} style={{ margin: 0 }}>{title}</Title>}
      </div>
      <Space>
        {enableSearch && (
          <Search
            placeholder="Search..."
            allowClear
            onSearch={handleSearch}
            style={{ width: 200 }}
            prefix={<SearchOutlined />}
          />
        )}
        {enableRefresh && (
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={config.loading}
          >
            Refresh
          </Button>
        )}
        {enableExport && (
          <Button
            icon={<DownloadOutlined />}
            onClick={handleExport}
          >
            Export
          </Button>
        )}
      </Space>
    </div>
  );

  if (useAgGrid) {
    return (
      <Card className={className} style={style}>
        {renderToolbar()}
        <div 
          className="ag-theme-alpine" 
          style={{ height, width: '100%' }}
        >
          <AgGridReact
            {...gridOptions}
            onGridReady={onGridReady}
            loading={config.loading}
          />
        </div>
      </Card>
    );
  }

  // Fallback to Ant Design Table
  return (
    <Card className={className} style={style}>
      {renderToolbar()}
      <Table
        columns={config.columns.map(col => ({
          ...col,
          dataIndex: col.dataIndex || col.key,
          sorter: col.sortable,
          filterDropdown: col.filterable ? undefined : null
        }))}
        dataSource={config.dataSource}
        loading={config.loading}
        pagination={config.pagination ? {
          current: config.pagination.current,
          pageSize: config.pagination.pageSize,
          total: config.pagination.total,
          showSizeChanger: config.pagination.showSizeChanger,
          showQuickJumper: config.pagination.showQuickJumper,
          showTotal: (total, range) => 
            `${range[0]}-${range[1]} of ${total} items`
        } : false}
        rowSelection={config.rowSelection ? {
          type: config.rowSelection.type,
          selectedRowKeys: config.rowSelection.selectedRowKeys,
          onChange: config.rowSelection.onChange
        } : undefined}
        onRow={config.onRow}
        scroll={{ y: height }}
      />
    </Card>
  );
}

// Specialized components for common use cases
interface UserTableProps {
  users: any[];
  loading?: boolean;
  onEdit?: (user: any) => void;
  onDelete?: (user: any) => void;
}

export function UserTable({ users, loading, onEdit, onDelete }: UserTableProps) {
  const config: TableConfig = {
    columns: [
      {
        key: 'name',
        title: 'Name',
        dataIndex: 'name',
        sortable: true,
        filterable: true
      },
      {
        key: 'email',
        title: 'Email',
        dataIndex: 'email',
        sortable: true,
        filterable: true
      },
      {
        key: 'role',
        title: 'Role',
        dataIndex: 'role',
        sortable: true,
        filterable: true
      },
      {
        key: 'status',
        title: 'Status',
        dataIndex: 'status',
        render: (status) => (
          <span style={{ 
            color: status === 'active' ? 'green' : 'red' 
          }}>
            {status}
          </span>
        )
      },
      {
        key: 'actions',
        title: 'Actions',
        render: (_, record) => (
          <Space>
            <Button size="small" onClick={() => onEdit?.(record)}>
              Edit
            </Button>
            <Button size="small" danger onClick={() => onDelete?.(record)}>
              Delete
            </Button>
          </Space>
        )
      }
    ],
    dataSource: users,
    loading,
    pagination: {
      current: 1,
      pageSize: 10,
      total: users.length,
      showSizeChanger: true,
      showQuickJumper: true
    }
  };

  return (
    <DataTable
      config={config}
      title="Users"
      useAgGrid={true}
    />
  );
}
