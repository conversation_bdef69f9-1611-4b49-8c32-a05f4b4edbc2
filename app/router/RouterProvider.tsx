import React from 'react';
import { RouterProvider as ReactRouterProvider } from 'react-router';
import { router } from './index';
import { AuthProvider } from '@/hooks';

/**
 * Main Router Provider Component
 * Wraps the application with necessary providers and router
 */

interface AppRouterProviderProps {
  children?: React.ReactNode;
}

export function RouterProvider({ children }: AppRouterProviderProps) {
  return (
    <AuthProvider>
      <ReactRouterProvider router={router} />
      {children}
    </AuthProvider>
  );
}
