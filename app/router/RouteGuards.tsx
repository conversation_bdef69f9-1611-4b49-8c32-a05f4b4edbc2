import React from 'react';
import { Navigate, useLocation } from 'react-router';
import { useAuth, usePermissions } from '@/hooks';
import { Permission, UserRole } from '@/types';
import { navigation } from '@/config/routes';
import { Spin } from 'antd';

/**
 * Route guard components for protecting routes based on authentication and permissions
 */

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermissions?: Permission[];
  requiredRole?: UserRole;
  fallbackPath?: string;
}

interface PublicRouteProps {
  children: React.ReactNode;
  redirectPath?: string;
}

interface RouteGuardProps {
  children: React.ReactNode;
  condition: boolean;
  fallbackPath: string;
  loading?: boolean;
}

/**
 * Protected Route Component
 * Requires authentication and optionally specific permissions/roles
 */
export function ProtectedRoute({
  children,
  requiredPermissions = [],
  requiredRole,
  fallbackPath = navigation.login()
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, user } = useAuth();
  const { canAccess } = usePermissions();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" />
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return (
      <Navigate 
        to={fallbackPath} 
        state={{ from: location.pathname }} 
        replace 
      />
    );
  }

  // Check role requirement
  if (requiredRole && user?.role !== requiredRole) {
    return (
      <Navigate 
        to={navigation.unauthorized()} 
        replace 
      />
    );
  }

  // Check permission requirements
  if (requiredPermissions.length > 0) {
    const hasAccess = canAccess(requiredPermissions);
    if (!hasAccess) {
      return (
        <Navigate 
          to={navigation.unauthorized()} 
          replace 
        />
      );
    }
  }

  return <>{children}</>;
}

/**
 * Public Route Component
 * Redirects authenticated users away from public pages (like login)
 */
export function PublicRoute({
  children,
  redirectPath = navigation.dashboard()
}: PublicRouteProps) {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" />
      </div>
    );
  }

  // Redirect authenticated users to dashboard
  if (isAuthenticated) {
    return <Navigate to={redirectPath} replace />;
  }

  return <>{children}</>;
}

/**
 * Generic Route Guard Component
 * Provides flexible route protection based on custom conditions
 */
export function RouteGuard({
  children,
  condition,
  fallbackPath,
  loading = false
}: RouteGuardProps) {
  // Show loading spinner if specified
  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" />
      </div>
    );
  }

  // Redirect if condition is not met
  if (!condition) {
    return <Navigate to={fallbackPath} replace />;
  }

  return <>{children}</>;
}

/**
 * Higher-Order Component for route protection
 */
export function withRouteGuard<P extends object>(
  Component: React.ComponentType<P>,
  guardProps: Omit<ProtectedRouteProps, 'children'>
) {
  return function GuardedComponent(props: P) {
    return (
      <ProtectedRoute {...guardProps}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

/**
 * Hook for programmatic route protection
 */
export function useRouteGuard(
  requiredPermissions: Permission[] = [],
  requiredRole?: UserRole
) {
  const { isAuthenticated, user } = useAuth();
  const { canAccess } = usePermissions();

  const canAccessRoute = React.useMemo(() => {
    if (!isAuthenticated) return false;
    
    if (requiredRole && user?.role !== requiredRole) return false;
    
    if (requiredPermissions.length > 0) {
      return canAccess(requiredPermissions);
    }
    
    return true;
  }, [isAuthenticated, user, requiredRole, requiredPermissions, canAccess]);

  return {
    canAccessRoute,
    isAuthenticated,
    user
  };
}

/**
 * Component for conditional rendering based on permissions
 */
interface ConditionalRenderProps {
  children: React.ReactNode;
  requiredPermissions?: Permission[];
  requiredRole?: UserRole;
  fallback?: React.ReactNode;
  requireAuth?: boolean;
}

export function ConditionalRender({
  children,
  requiredPermissions = [],
  requiredRole,
  fallback = null,
  requireAuth = true
}: ConditionalRenderProps) {
  const { isAuthenticated, user } = useAuth();
  const { canAccess } = usePermissions();

  // Check authentication requirement
  if (requireAuth && !isAuthenticated) {
    return <>{fallback}</>;
  }

  // Check role requirement
  if (requiredRole && user?.role !== requiredRole) {
    return <>{fallback}</>;
  }

  // Check permission requirements
  if (requiredPermissions.length > 0 && !canAccess(requiredPermissions)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
