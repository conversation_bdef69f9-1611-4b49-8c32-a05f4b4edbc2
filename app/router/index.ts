import { createBrowserRouter } from "react-router";
import { generateReactRouterConfig } from "@/config/routes";

/**
 * React Router v7 flat routes implementation
 * Centralized router configuration with security and permission checks
 */

// Generate React Router configuration from our route configs
export const router = createBrowserRouter(generateReactRouterConfig());

// Export route configurations for use in components
export { routeConfigs, navigation, routeManager } from "@/config/routes";

// Route guards and utilities
export { ProtectedRoute, PublicRoute, RouteGuard } from "./RouteGuards";
// export { RouterProvider } from './RouterProvider';
export { RouterProvider } from "./RouterProvider";
