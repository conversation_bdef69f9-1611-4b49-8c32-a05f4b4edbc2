{"include": ["**/*", "**/.server/**/*", "**/.client/**/*", ".react-router/types/**/*"], "compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES2022"], "types": ["node", "vite/client"], "target": "ES2022", "module": "ES2022", "moduleResolution": "bundler", "jsx": "react-jsx", "rootDirs": [".", "./.react-router/types"], "baseUrl": ".", "paths": {"~/*": ["./app/*"], "@/*": ["./app/*"], "@/api/*": ["./app/api/*"], "@/assets/*": ["./app/assets/*"], "@/components/*": ["./app/components/*"], "@/config/*": ["./app/config/*"], "@/constants/*": ["./app/constants/*"], "@/hooks/*": ["./app/hooks/*"], "@/pages/*": ["./app/pages/*"], "@/router/*": ["./app/router/*"], "@/types/*": ["./app/types/*"], "@/utils/*": ["./app/utils/*"]}, "esModuleInterop": true, "verbatimModuleSyntax": true, "noEmit": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true}}